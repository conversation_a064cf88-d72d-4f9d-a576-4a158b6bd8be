{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order-confirm.vue?0f8c", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order-confirm.vue?5f39", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order-confirm.vue?29bc", "uni-app:///pages/pagesHome/order-confirm.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order-confirm.vue?19ee", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/order-confirm.vue?d11a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "remark", "goodsInfo", "name", "spec", "price", "quantity", "weight", "deliveryFee", "totalAmount", "orderData", "<PERSON><PERSON><PERSON><PERSON>", "selectedDeliveryTime", "deliveryFeeInfo", "isCalculatingFee", "freeShipping", "onLoad", "console", "uni", "onUnload", "methods", "getDefaultAddress", "that", "code", "msg", "selectAddress", "url", "selectTime", "calculateTotal", "calculateDeliveryFee", "address_id", "machine_id", "total_weight", "type", "icon", "title", "duration", "submitOrder", "goods_id", "goods_num", "order_mode", "user_address_id", "delivery_time"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmGjvB;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;IAEA;MACA;QACA;QACA;UACAd;UACAC;UACAC;UACAC;UACAC;QACA;QACA;QACA;QACA;QACA;MACA;QACAU;MACA;IACA;;IAEA;IACA;;IAEA;IACAC;MACA;MACA;MACA;IACA;;IAEA;IACAA;MACA;IACA;EACA;EACAC;IACA;IACAD;IACAA;EACA;EACAE;IACA;IACAC;MACA;MACAC;QACA;UAAAC;UAAAvB;UAAAwB;QACA;UACA;UACA;YAAA;UAAA;UACA;YACAF;UACA;YACA;YACAA;UACA;UACA;UACAA;QACA;MACA;QACAL;MACA;IACA;IACAQ;MACA;MACAP;QACAQ;MACA;IACA;IACAC;MACA;MACA;MACAT;QACAQ;MACA;IACA;IACAE;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACAZ;UACAN;UACAD;QACA;QACA;MACA;;MAEA;MACA;QACAO;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEAK;MAEA;QACAQ;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MAEAhB;MAEAK;QACA;UAAAC;UAAAvB;UAAAwB;QACA;UACAF;UACAA;UACAA;UACA;UACAA;QACA;UACAJ;YACAgB;YACAC;YACAC;UACA;UACA;UACAd;UACAA;QACA;MACA;QACAL;QACAC;UACAgB;UACAC;UACAC;QACA;QACA;QACAd;QACAA;MACA;QACAA;MACA;IACA;IACAe;MACA;;MAEA;MACA;QACAnB;UACAgB;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAlB;UACAgB;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACA;QACAE;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;MACA;MAEAzB;;MAEA;MACAC;QACAiB;MACA;;MAEA;MACAb;QACAJ;QACA;UAAAK;UAAAvB;UAAAwB;QACA;UACA;UACA;UACAN;YACAQ;UACA;QACA;UACAR;YACAgB;YACAC;YACAC;UACA;QACA;MACA;QACAlB;QACAD;QACAC;UACAgB;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnVA;AAAA;AAAA;AAAA;AAAsjC,CAAgB,s+BAAG,EAAC,C;;;;;;;;;;;ACA1kC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesHome/order-confirm.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesHome/order-confirm.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order-confirm.vue?vue&type=template&id=0eaba40c&scoped=true&\"\nvar renderjs\nimport script from \"./order-confirm.vue?vue&type=script&lang=js&\"\nexport * from \"./order-confirm.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order-confirm.vue?vue&type=style&index=0&id=0eaba40c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0eaba40c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesHome/order-confirm.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-confirm.vue?vue&type=template&id=0eaba40c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.goodsInfo.price.toFixed(2)\n  var g1 = (_vm.goodsInfo.price * _vm.goodsInfo.quantity).toFixed(2)\n  var g2 =\n    !_vm.isCalculatingFee && !_vm.freeShipping\n      ? _vm.deliveryFee.toFixed(2)\n      : null\n  var g3 = _vm.totalAmount.toFixed(2)\n  var g4 = _vm.totalAmount.toFixed(2)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-confirm.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-confirm.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 收货地址 -->\n\t\t<view class=\"address-section\">\n\t\t\t<view class=\"address-item\" @click=\"selectAddress\" v-if=\"selectedAddress\">\n\t\t\t\t<view class=\"address-icon\">\n\t\t\t\t\t<text class=\"location-icon\">📍</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"address-content\">\n\t\t\t\t\t<view class=\"address-info\">\n\t\t\t\t\t\t<text class=\"name\">{{selectedAddress.consignee}} {{selectedAddress.phone}}</text>\n\t\t\t\t\t\t<text class=\"arrow\">›</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"address-detail\">{{selectedAddress.province_name + selectedAddress.city_name + selectedAddress.area_name + selectedAddress.address}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"address-item\" @click=\"selectAddress\" v-else>\n\t\t\t\t<view class=\"address-icon\">\n\t\t\t\t\t<text class=\"location-icon\">📍</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"address-content\">\n\t\t\t\t\t<view class=\"address-info\">\n\t\t\t\t\t\t<text class=\"name\" style=\"color: #999;\">请选择收货地址</text>\n\t\t\t\t\t\t<text class=\"arrow\">›</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"address-divider\"></view>\n\t\t</view>\n\n\t\t<!-- 配送时间 -->\n\t\t<view class=\"delivery-time\">\n\t\t\t<view class=\"time-label\">配送时间</view>\n\t\t\t<view class=\"time-select\" @click=\"selectTime\">\n\t\t\t\t<text v-if=\"selectedDeliveryTime\">{{selectedDeliveryTime.date_text}} {{selectedDeliveryTime.text}}</text>\n\t\t\t\t<text v-else style=\"color: #999;\">选择配送时间</text>\n\t\t\t\t<text class=\"arrow\">›</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 商品信息 -->\n\t\t<view class=\"goods-section\">\n\t\t\t<view class=\"goods-item\">\n\t\t\t\t<view class=\"goods-image\">\n\t\t\t\t\t<image src=\"/static/ice-cube.png\" mode=\"aspectFit\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"goods-info\">\n\t\t\t\t\t<view class=\"goods-name\">{{goodsInfo.name}}</view>\n\t\t\t\t\t<view class=\"goods-spec\">下单规格：{{goodsInfo.spec}}</view>\n\t\t\t\t\t<view class=\"goods-price\">¥ {{goodsInfo.price.toFixed(2)}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 备注 -->\n\t\t<view class=\"remark-section\">\n\t\t\t<text class=\"remark-label\">备注</text>\n\t\t\t<input class=\"remark-input\" placeholder=\"建议留言告知先生买家冰淇淋\" v-model=\"remark\" />\n\t\t</view>\n\n\t\t<!-- 价格明细 -->\n\t\t<view class=\"price-section\">\n\t\t\t<view class=\"price-item\">\n\t\t\t\t<text class=\"price-label\">商品金额</text>\n\t\t\t\t<text class=\"price-value\">¥ {{(goodsInfo.price * goodsInfo.quantity).toFixed(2)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"price-item\">\n\t\t\t\t<text class=\"price-label\">配送费</text>\n\t\t\t\t<view class=\"delivery-fee\">\n\t\t\t\t\t<text v-if=\"isCalculatingFee\" style=\"color: #999;\">计算中...</text>\n\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t<text v-if=\"freeShipping\" class=\"free-delivery\">免配送费</text>\n\t\t\t\t\t\t<text v-else class=\"original-fee\">¥ {{deliveryFee.toFixed(2)}}</text>\n\t\t\t\t\t\t<text v-if=\"deliveryFeeInfo && deliveryFeeInfo.distance\" class=\"distance-info\">\n\t\t\t\t\t\t\t(距离{{deliveryFeeInfo.distance}}km)\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</template>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"price-item total\">\n\t\t\t\t<text class=\"price-label\">总金额</text>\n\t\t\t\t<text class=\"price-value total-price\">¥ {{totalAmount.toFixed(2)}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部提交 -->\n\t\t<view class=\"bottom-bar\">\n\t\t\t<view class=\"total-info\">\n\t\t\t\t<text class=\"total-count\">共{{goodsInfo.quantity}}件</text>\n\t\t\t\t<text class=\"total-amount\">合计：¥ {{totalAmount.toFixed(2)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"submit-btn\" @click=\"submitOrder\">\n\t\t\t\t<text>提交订单</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tremark: '',\n\t\t\t\tgoodsInfo: {\n\t\t\t\t\tname: '冰块',\n\t\t\t\t\tspec: '冰块10kg',\n\t\t\t\t\tprice: 79.00,\n\t\t\t\t\tquantity: 1,\n\t\t\t\t\tweight: 10 // 商品重量（公斤）\n\t\t\t\t},\n\t\t\t\tdeliveryFee: 0,\n\t\t\t\ttotalAmount: 0,\n\t\t\t\torderData: null, // 保存从vending页面传递的订单数据\n\t\t\t\tselectedAddress: null, // 选中的收货地址\n\t\t\t\tselectedDeliveryTime: null, // 选中的配送时间\n\t\t\t\tdeliveryFeeInfo: null, // 配送费详情\n\t\t\t\tisCalculatingFee: false, // 是否正在计算配送费\n\t\t\t\tfreeShipping: false // 是否免配送费\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 接收从vending页面传递的参数\n\t\t\tconsole.log('订单确认页面参数:', options)\n\n\t\t\tif (options.goodsInfo) {\n\t\t\t\ttry {\n\t\t\t\t\tconst goodsData = JSON.parse(decodeURIComponent(options.goodsInfo))\n\t\t\t\t\tthis.goodsInfo = {\n\t\t\t\t\t\tname: goodsData.goods_name || '冰块',\n\t\t\t\t\t\tspec: goodsData.goods_spec || '冰块10kg',\n\t\t\t\t\t\tprice: parseFloat(goodsData.goods_price) || 79.00,\n\t\t\t\t\t\tquantity: parseInt(goodsData.goods_num) || 1,\n\t\t\t\t\t\tweight: parseFloat(goodsData.goods_weight) || 10 // 从商品信息中获取重量\n\t\t\t\t\t}\n\t\t\t\t\t// 重新计算总金额\n\t\t\t\t\tthis.calculateTotal()\n\t\t\t\t\t// 保存商品信息用于后续创建订单\n\t\t\t\t\tthis.orderData = goodsData\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('解析商品信息失败:', error)\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 获取默认收货地址\n\t\t\tthis.getDefaultAddress()\n\n\t\t\t// 监听地址选择返回\n\t\t\tuni.$on('updateData', (address) => {\n\t\t\t\tthis.selectedAddress = address\n\t\t\t\t// 地址变化时重新计算运费\n\t\t\t\tthis.calculateDeliveryFee()\n\t\t\t})\n\n\t\t\t// 监听配送时间选择返回\n\t\t\tuni.$on('updateDeliveryTime', (deliveryTime) => {\n\t\t\t\tthis.selectedDeliveryTime = deliveryTime\n\t\t\t})\n\t\t},\n\t\tonUnload() {\n\t\t\t// 移除事件监听\n\t\t\tuni.$off('updateData')\n\t\t\tuni.$off('updateDeliveryTime')\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取默认收货地址\n\t\t\tgetDefaultAddress() {\n\t\t\t\tlet that = this\n\t\t\t\tthat.$api.Address({}).then(res => {\n\t\t\t\t\tconst { code, data, msg } = res.data\n\t\t\t\t\tif (code === 1 && data.length > 0) {\n\t\t\t\t\t\t// 查找默认地址\n\t\t\t\t\t\tconst defaultAddress = data.find(item => item.is_default == 1)\n\t\t\t\t\t\tif (defaultAddress) {\n\t\t\t\t\t\t\tthat.selectedAddress = defaultAddress\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果没有默认地址，使用第一个地址\n\t\t\t\t\t\t\tthat.selectedAddress = data[0]\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 获取到地址后计算运费\n\t\t\t\t\t\tthat.calculateDeliveryFee()\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tconsole.error('获取地址失败:', err)\n\t\t\t\t})\n\t\t\t},\n\t\t\tselectAddress() {\n\t\t\t\t// 跳转到地址选择页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/pagesHome/address?type=2'\n\t\t\t\t})\n\t\t\t},\n\t\t\tselectTime() {\n\t\t\t\t// 跳转到配送时间选择页面\n\t\t\t\tlet machine_no = this.orderData ? this.orderData.machine_no : ''\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/pagesHome/delivery-time?machine_no=' + machine_no\n\t\t\t\t})\n\t\t\t},\n\t\t\tcalculateTotal() {\n\t\t\t\t// 计算总金额：商品价格 * 数量 + 配送费\n\t\t\t\tconst goodsTotal = this.goodsInfo.price * this.goodsInfo.quantity\n\t\t\t\tthis.totalAmount = goodsTotal + this.deliveryFee\n\t\t\t},\n\n\t\t\t// 计算配送费用\n\t\t\tcalculateDeliveryFee() {\n\t\t\t\tlet that = this\n\n\t\t\t\t// 检查必要条件\n\t\t\t\tif (!that.selectedAddress || !that.orderData) {\n\t\t\t\t\tconsole.log('缺少必要条件:', {\n\t\t\t\t\t\tselectedAddress: !!that.selectedAddress,\n\t\t\t\t\t\torderData: !!that.orderData\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 检查地址是否有ID\n\t\t\t\tif (!that.selectedAddress.id) {\n\t\t\t\t\tconsole.log('地址缺少ID信息')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 检查是否有机器ID\n\t\t\t\tif (!that.orderData.machine_id) {\n\t\t\t\t\tconsole.log('订单数据缺少机器ID')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthat.isCalculatingFee = true\n\n\t\t\t\tlet params = {\n\t\t\t\t\taddress_id: that.selectedAddress.id,\n\t\t\t\t\tmachine_id: that.orderData.machine_id, // 修正：直接使用传递过来的机器ID\n\t\t\t\t\ttotal_weight: that.goodsInfo.weight, // 修正：直接使用商品重量，不再乘以数量\n\t\t\t\t\ttype: 1 // 用户配送费\n\t\t\t\t}\n\n\t\t\t\tconsole.log('计算配送费参数:', params)\n\n\t\t\t\tthat.$api.calculateDeliveryFee(params).then(res => {\n\t\t\t\t\tconst { code, data, msg } = res.data\n\t\t\t\t\tif (code === 1) {\n\t\t\t\t\t\tthat.deliveryFee = data.delivery_fee\n\t\t\t\t\t\tthat.deliveryFeeInfo = data\n\t\t\t\t\t\tthat.freeShipping = data.free_shipping\n\t\t\t\t\t\t// 重新计算总金额\n\t\t\t\t\t\tthat.calculateTotal()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\t\ttitle: msg,\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t})\n\t\t\t\t\t\t// 计算失败时设置默认配送费\n\t\t\t\t\t\tthat.deliveryFee = 10.00\n\t\t\t\t\t\tthat.calculateTotal()\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tconsole.error('计算配送费失败:', err)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\ttitle: '计算配送费失败',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t\t// 计算失败时设置默认配送费\n\t\t\t\t\tthat.deliveryFee = 10.00\n\t\t\t\t\tthat.calculateTotal()\n\t\t\t\t}).finally(() => {\n\t\t\t\t\tthat.isCalculatingFee = false\n\t\t\t\t})\n\t\t\t},\n\t\t\tsubmitOrder() {\n\t\t\t\tlet that = this\n\n\t\t\t\t// 验证必要信息\n\t\t\t\tif (!that.selectedAddress) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\ttitle: '请选择收货地址',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!that.orderData || !that.orderData.machine_id) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\ttitle: '订单信息有误',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 构建订单参数\n\t\t\t\tlet params = {\n\t\t\t\t\t'machine-no': that.orderData.machine_no,\n\t\t\t\t\tgoods_id: that.orderData.goods_id,\n\t\t\t\t\tgoods_num: that.goodsInfo.weight, // 外卖模式使用重量作为数量\n\t\t\t\t\torder_mode: 2, // 外卖模式\n\t\t\t\t\tuser_address_id: that.selectedAddress.id,\n\t\t\t\t\tdelivery_time: that.selectedDeliveryTime || {}\n\t\t\t\t}\n\n\t\t\t\tconsole.log('提交外卖订单参数:', params)\n\n\t\t\t\t// 显示加载状态\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '创建订单中...'\n\t\t\t\t})\n\n\t\t\t\t// 调用创建外卖订单接口\n\t\t\t\tthat.$api.createDeliveryOrder(params).then(res => {\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\tconst { code, data, msg } = res.data\n\t\t\t\t\tif (code === 1) {\n\t\t\t\t\t\t// 订单创建成功，跳转到支付页面\n\t\t\t\t\t\t// 外卖订单也是售货机订单，使用type=1\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/pagesPay/index?type=1&order_sn=' + data.order_sn\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\t\ttitle: msg || '创建订单失败',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\tconsole.error('创建订单失败:', err)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\ttitle: '创建订单失败',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.container {\n\t\tbackground: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\n\n\n\t.icon-back {\n\t\tfont-size: 40rpx;\n\t\tcolor: #333;\n\t}\n\n\t/* 收货地址 */\n\t.address-section {\n\t\tbackground: #fff;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.address-item {\n\t\tdisplay: flex;\n\t\tpadding: 30rpx;\n\t\talign-items: flex-start;\n\t}\n\n\t.address-icon {\n\t\tmargin-right: 20rpx;\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.location-icon {\n\t\tfont-size: 32rpx;\n\t}\n\n\t.address-content {\n\t\tflex: 1;\n\t}\n\n\t.address-info {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.name {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n\n\t.arrow {\n\t\tfont-size: 32rpx;\n\t\tcolor: #999;\n\t}\n\n\t.address-detail {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 40rpx;\n\t}\n\n\t.address-divider {\n\t\theight: 2rpx;\n\t\tbackground: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);\n\t\tmargin: 0 30rpx;\n\t}\n\n\t/* 配送时间 */\n\t.delivery-time {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tbackground: #fff;\n\t\tpadding: 30rpx;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.time-label {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t}\n\n\t.time-select {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 10rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t/* 商品信息 */\n\t.goods-section {\n\t\tbackground: #fff;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 30rpx;\n\t}\n\n\t.goods-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.goods-image {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.goods-image image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.goods-info {\n\t\tflex: 1;\n\t}\n\n\t.goods-name {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.goods-spec {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t\tmargin-bottom: 15rpx;\n\t}\n\n\t.goods-price {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n\n\t/* 备注 */\n\t.remark-section {\n\t\tbackground: #fff;\n\t\tpadding: 30rpx;\n\t\tmargin-top: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.remark-label {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t\tmargin-right: 30rpx;\n\t\tmin-width: 80rpx;\n\t}\n\n\t.remark-input {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\n\t/* 价格明细 */\n\t.price-section {\n\t\tbackground: #fff;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 30rpx;\n\t}\n\n\t.price-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.price-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.price-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\n\t.price-value {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\n\t.delivery-fee {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 10rpx;\n\t}\n\n\t.original-fee {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t\ttext-decoration: line-through;\n\t}\n\n\t.free-delivery {\n\t\tfont-size: 24rpx;\n\t\tcolor: #ff6b6b;\n\t}\n\n\t.distance-info {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-left: 10rpx;\n\t}\n\n\t.total {\n\t\tborder-top: 1rpx solid #f0f0f0;\n\t\tpadding-top: 20rpx;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.total-price {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #ff6b6b;\n\t}\n\n\t/* 底部提交栏 */\n\t.bottom-bar {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 20rpx 30rpx;\n\t\tborder-top: 1rpx solid #f0f0f0;\n\t\tpadding-bottom: calc(20rpx + env(safe-area-inset-bottom));\n\t}\n\n\t.total-info {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t}\n\n\t.total-count {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.total-amount {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n\n\t.submit-btn {\n\t\tbackground: #4A90E2;\n\t\tcolor: #fff;\n\t\tpadding: 20rpx 60rpx;\n\t\tborder-radius: 50rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-confirm.vue?vue&type=style&index=0&id=0eaba40c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-confirm.vue?vue&type=style&index=0&id=0eaba40c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757298753615\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}