<template>
	<view class="content">
		<view class="banner_bg">
			<swiper autoplay='true' circular="true" interval='5000' duration='500' class='banner'>
				<swiper-item class="item_image" v-for="(item,index) in banner">
					<image :src='api + item.image' class='swiper_image' mode="aspectFill"></image>
				</swiper-item>
			</swiper>
		</view>
		<view class="nr">
			<view class="title">
				<view class="xx">
					<view>{{notice.title}}</view>
					<view>{{notice.content}}</view>
				</view>
				<view class="text" @click="getCall(1)">配送电话：{{ps_tel}}</view>
			</view>
			<view class="specification">
				<!-- 购买方式选择 -->
				<view class="delivery-type">
					<view class="delivery-buttons">
						<view :class="delivery_type == 1?'delivery-btn active':'delivery-btn'" @click="setDeliveryType(1)">
							<text>自提</text>
							<text class="distance">距离2.7km</text>
						</view>
						<view :class="delivery_type == 2?'delivery-btn active':'delivery-btn'" @click="setDeliveryType(2)">
							<text>外卖</text>
						</view>
					</view>
				</view>

				<view class="bt">规格选择</view>
				<view class="list" v-if="way.length > 0">
					<view :class="item.id == goods_id?'xx on':'xx'" v-for="item in way" @click="setGoods(item)">
						<view>{{item.name}}</view>
						<text>￥{{item.spec_num > 0?item.price:item.price + '/' + item.spec}}</text>
					</view>
				</view>
				<view class="btn" @click="getSubmit">确认订单 {{total?'￥' + total:''}}</view>
				<!-- <view class="btn" v-else @click="getScan">扫码下单</view> -->
			</view>
		</view>
		<view class="message">
			<view class="xx" @click="getCall(2)">
				<image src="/static/icon_my7.png" mode="widthFix"></image>
				<view class="text">
					<view>在线客服</view>
					<view>24小时在线守护</view>
				</view>
			</view>
			<navigator url="/pages/pagesMy/message?type=1" hover-class="none" class="xx">
				<image src="/static/icon_my6.png" mode="widthFix"></image>
				<view class="text">
					<view>意见反馈</view>
					<view>24让我们更懂您</view>
				</view>
			</navigator>
		</view>
		
		
		<uni-popup ref="showModal" type="center" :mask-click="false">
			<view class="tc_input">
				<view class="title">自定义</view>
				<input type="number" placeholder="请输入重量(KG)" v-model="goods_num">
				<view class="btn">
					<view @click="getClose(1)">取消</view>
					<view @click="getClose(2)">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				api: '', //域名

				kefu_tel: '',
				ps_tel: '',
				machine_no: '',
				banner: [],
				way: [],// 规格
				notice: '',

				goods_id: '',
				goods_num: '',
				price: '',
				total: '',

				delivery_type: 1, // 购买方式 1:自提 2:外卖

				isBtn: false,
			}
		},
		onLoad(option) {
			this.api = this.$http.apiPic
			// 扫码进入
			if (option.q) {
				let qrUrl = decodeURIComponent(option.q)
				let machine_no = this.$util.getQueryString(qrUrl, 'machine-no')
				this.machine_no = machine_no
			}
			if (option.machine_no) {
				this.machine_no = option.machine_no
			}
			this.getTel()
			this.getInfo()
			this.getPstel()
		},
		onShow() {
			
		},
		watch:{
			goods_num(newVal, oldVal) {
				if(this.price){
					this.total = (Number(newVal) * Number(this.price)).toFixed(2);
				}
			},
		},
		onShareAppMessage(res) {
			if (res.from === 'button') {
				console.log(res.target)
			}
			return {
				path: '/pages/index/index'
			}
		},
		methods: {
			getPstel(){
				let that = this;
				let params = {};
				that.$api.getPstel(params).then(res => {
					const {
						code,
						data,
						msg,
						time
					} = res.data
					if (code == 1) {
						that.ps_tel = data
					}else{
						uni.showToast({
							icon: "none",
							title: msg,
							duration: 2000
						});
					}
				})
				.catch((err) => {
						
				})
			},
			getTel(){
				let that = this;
				let params = {};
				that.$api.tel(params).then(res => {
					const {
						code,
						data,
						msg,
						time
					} = res.data
					if (code == 1) {
						that.kefu_tel = data
					}else{
						uni.showToast({
							icon: "none",
							title: msg,
							duration: 2000
						});
					}
				})
				.catch((err) => {
						
				})
			},
			// 柜子信息
			getInfo(){
				let that = this;
				let params = {
					'machine-no': that.machine_no,
				};
				that.$api.machineindex(params).then(res => {
					const {
						code,
						data,
						msg,
						time
					} = res.data
					if (code == 1) {
						that.banner = data.ad
						that.way = data.way
						that.notice = data.notice
					}else{
						uni.showToast({
							icon: "none",
							title: msg,
							duration: 2000
						});
					}
				})
				.catch((err) => {
						
				})
			},
			
			// 选择购买方式
			setDeliveryType(type){
				this.delivery_type = type
			},

			// 选择规格
			setGoods(e){
				let that = this
				that.goods_id = e.id
				if(e.spec_num > 0){
					that.goods_num = 1
					that.price = ''
					that.total = e.price
				}else{
					that.goods_num = ''
					that.price = e.price
					that.$refs.showModal.open()
				}
			},
			getClose(e){
				this.$refs.showModal.close()
				if(e == 1){
					this.goods_id = ''
					this.goods_num = ''
					this.price = ''
					this.total = ''
				}
			},
			// 下单
			getSubmit(){
				let that = this;
				if(that.isBtn){
					return
				}
				if (!that.$cache.fetchCache('token')) {
					that.$util.toLogin()
					return
				}
				if(!that.goods_id){
					uni.showToast({
						icon: "none",
						title: '请选择规格',
						duration: 2000
					});
					return
				}

				// 如果是外卖模式，跳转到订单确认页面
				if (that.delivery_type == 2) {
					// 获取当前选中的商品信息
					let selectedGoods = that.way.find(item => item.id == that.goods_id)

					// 计算实际重量：如果是自定义重量，使用goods_num作为重量；否则使用商品默认重量
					let actualWeight = selectedGoods ? (selectedGoods.weight || 10) : 10
					let actualQuantity = 1 // 外卖模式默认数量为1

					// 如果选择的是自定义规格，goods_num表示重量而不是数量
					if (selectedGoods && selectedGoods.name && selectedGoods.name.includes('自定义')) {
						actualWeight = parseFloat(that.goods_num) || 10
						actualQuantity = 1
					}

					let goodsInfo = {
						machine_no: that.machine_no,
						goods_id: that.goods_id,
						goods_num: actualQuantity, // 修正：使用实际数量
						goods_name: selectedGoods ? selectedGoods.name : '冰块',
						goods_price: that.price || that.total || (selectedGoods ? selectedGoods.price : 79.00),
						goods_spec: selectedGoods ? selectedGoods.spec : '冰块10kg',
						goods_weight: actualWeight // 修正：使用实际重量
					}
					uni.navigateTo({
						url: '/pages/pagesHome/order-confirm?goodsInfo=' + encodeURIComponent(JSON.stringify(goodsInfo))
					})
					return
				}

				// 自提模式：保持原有逻辑
				that.isBtn = true
				let params = {
					'machine-no': that.machine_no,
					goods_id: that.goods_id,
					goods_num: that.goods_num,
				};
				that.$api.createOrderM(params).then(res => {
					const {
						code,
						data,
						msg,
						time
					} = res.data
					if (code == 1) {
						that.isBtn = false
						// 自提订单直接跳转支付页面
						uni.navigateTo({
							url: '/pages/pagesPay/index?type=1&order_sn=' + data.order_sn
						})
					}else{
						that.isBtn = false
						uni.showToast({
							icon: "none",
							title: msg,
							duration: 2000
						});
					}
				})
				.catch((err) => {

				})
			},
			// 客服热线
			getCall(e) {
				let that = this
				uni.makePhoneCall({
					phoneNumber: e == 1?that.ps_tel:that.kefu_tel
				}).catch((res) => {
					
				})
			},
		}
	}
</script>

<style>
	.content{
		padding-bottom: 30rpx;
	}
	.banner_bg .banner {
		width: 100%;
		height: 488rpx;
	}

	.banner_bg .banner .swiper_image {
		width: 100%;
		height: 100%;
	}
	.nr {
		background: linear-gradient( 180deg, #BAD7FF 0%, #FFFFFF 32%);
	}
	.nr .title{
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: space-between;
		padding: 28rpx 30rpx;
	}
	.nr .title .xx view{
		font-size: 28rpx;
		line-height: 34rpx;
		color: #666;
	}
	.nr .title .xx view:first-child{
		font-size: 30rpx;
		line-height: 40rpx;
		color: #000;
		font-weight: bold;
		background: url(data:image/png;base64,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) no-repeat left bottom;
		background-size: auto 20rpx; 
	}
	.nr .title .text{
		font-size: 30rpx;
		line-height: 40rpx;
		color: #000;
	}

	.specification{
		background: #fff;
		padding: 25rpx 30rpx 40rpx;
		border-radius: 16rpx 16rpx 0 0;
	}
	.specification .bt {
		font-size: 30rpx;
		line-height: 40rpx;
		color: #161513;
		font-weight: bold;
		padding-left: 54rpx;
		margin-bottom: 32rpx;
		background: url(data:image/png;base64,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) no-repeat left center;
		background-size: 40rpx auto;
	}
	
	.specification .list {
		display: flex;
		flex-wrap: wrap;
	}
	.specification .list .xx{
		border: 1rpx solid #BFBFBF;
		margin-top: 30rpx;
		margin-right: 30rpx;
		border-radius: 16rpx;
		overflow: hidden;
		width: 210rpx;
		height: 142rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
	}
	.specification .list .xx.on{
		border-color: #3478FB;
		background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAYAAAA6RwvCAAAAAXNSR0IArs4c6QAAA+BJREFUWEfFmGuIVGUYx///c86cdXMbSXcDqciki2Ds7Qz0pYLUoiBDv5jFopa0QVHZRc/sFkQQ28zKgisVFnQxKoMiFxRSTM0PfUg7s65kkpEgQkHtRruVMXMuT5wZZ/Y2Z87ZmbHeb4f3ef7P733e63OIObTlT0hTw5XuXaSsJHALgGsBXHdJ4gLACwI5K8LD2T/Vo6ff5F9R5RnF0OjJ3UaPLwuwikQsio8IbAJfiqK9Yr3Gb8J8KoIkeuVWcZ1XATxAIhL0zIAiEBBD4mkvZvp5JggoULzTzHaRyjsE9LDRROkXICfibc6kGz4sZ18WxDDtJIC+arMQBFbIjrLVSqkDM21mgRg97tMUbzDKKKu1EZEtVlqfFmMaSGfSXqEABwFo1QaJ6Oe4Hu4d7o8dLtqXQNqevXhNTI+dArEwoliNZhxzvWzrcP/8n32hEohh5t4jualG9Tm6y7vfpvTNJZA2M7tco3KKgDJHpVrNHVu09pE0T+czYpjOXlLW1Kpajb+An1opbR2NbrmCC50xAPOqEQryaZoHdN2hwDonOPGTVJK+KL9rLTS2ZddQUfbWE2JRE7CrW8WSFsJxgfWDDs7/FhxBPG8tE9uct6BId71ApkL4mq4HPLgjBATcRSNpHyRwTz1AykH07nFx5LuKU+OHPuCDnCGwLAzkpsUFix9/KW9ZAwQE+J6JpD0OIF4J5PZlxPYuNW8y+IWLT76ePsJaIHxNEfwRCeSxlQq6V00eMQP7J2FqhbiUgAkf5AcAN1fKSKMO7HxERfuSyavJhzk0IqXdUVyYEdfEzHBnmTDtYyDuDFsj5WBGJwTN8QKcvzuqhPCn5isaSWc3IRvCQPz+cjC1QuTXCPg+O8zsepXKniggRZjXH1XRen0hE54APR9H2qKBIVzxHuKNT43FF8yP/0qgISpMvBF4+3EVS68mUkMuPj8eek4ESvtPyPG/J1r+/0tPOGSltbV5kPatuXZVpfVfPwME8BzxWkfSDYVngN8MM/cRyYejTk897ETkAyutb/S1SiCtL/xzg65pwwAW1CNIuAbHXdtuGx5oPD8NxP/oMJ37VMg+EIXz/PI1xwNWZ1KxA8UQs8sJ036OxKy6o55M4skzVr++c6pm2QKr08w9qZA7LkNZ4XgiWzJp/Y2ZAwsuOZP2CgKfEbiqTtkY84B1mVTsSDm9ioW10SuL4dp9IDdUvbXFv4a4m6r6ktXHgNfMlF1TadSJZK4DYB8Ed0deyAIXxCHHlZ6T2/WTYVmd068G43lpFtVerZD3A1gKchFEmvNByFGI+NXAOU9kP93YPmuAo2EAxf5/AU6Eibh+lv02AAAAAElFTkSuQmCC) no-repeat top -1rpx right -1rpx;
		background-size: 34rpx auto;
	}
	.specification .list .xx:nth-child(3n){
		margin-right: 0;
	}
	.specification .list .xx:nth-child(-n + 3){
		margin-top: 0;
	}
	.specification .list .xx view{
		font-size: 36rpx;
		line-height: 50rpx;
		color: #161513;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	.specification .list .xx text{
		font-size: 26rpx;
		line-height: 36rpx;
		color: #161513;
	}
	.specification .list .xx.on text{
		color: #FC451E;
		font-weight: bold;
	}
	.specification .btn {
		margin-top: 68rpx;
		font-size: 30rpx;
		line-height: 80rpx;
		color: #fff;
		background: #3478FB;
		border-radius: 40rpx;
		text-align: center;
		font-weight: bold;
	}

	/* 购买方式选择样式 */
	.delivery-type {
		margin-bottom: 40rpx;
		padding: 0 30rpx;
	}

	.delivery-buttons {
		display: flex;
		border-radius: 8rpx;
		overflow: hidden;
		height: 88rpx;
	}

	.delivery-btn {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background: #F5F5F5;
		border: 1rpx solid #E5E5E5;
		position: relative;
	}

	.delivery-btn:first-child {
		border-right: none;
	}

	.delivery-btn.active {
		background: #4A90E2;
		color: #fff;
	}

	.delivery-btn text {
		font-size: 28rpx;
		line-height: 40rpx;
		color: #333;
	}

	.delivery-btn.active text {
		color: #fff;
	}

	.delivery-btn .distance {
		font-size: 22rpx;
		line-height: 30rpx;
		color: #999;
	}

	.delivery-btn.active .distance {
		color: rgba(255, 255, 255, 0.8);
	}
	.message{
		background: #fff;
		padding: 20rpx 30rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: 24rpx;
	}
	.message .xx{
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		width: 50%;
	}
	.message .xx image{
		width: 44rpx;
		height: auto;
	}
	.message .xx .text{
		margin-left: 28rpx;
	}
	.message .xx .text view{
		font-size: 22rpx;
		line-height: 30rpx;
		color: #999;
	}
	.message .xx .text view:first-child{
		font-size: 30rpx;
		line-height: 38rpx;
		color: #161513;
		font-weight: bold;
	}
	.tc_input{
		width: 630rpx;
		background: #fff;
		border-radius: 20rpx;
		padding: 38rpx 30rpx 70rpx;
		box-sizing: border-box;
	}
	.tc_input .title{
		font-size: 28rpx;
		line-height: 40rpx;
		color: #161513;
		text-align: center;
		margin-bottom: 20rpx;
		font-weight: bold;
	}
	.tc_input input{
		font-size: 30rpx;
		line-height: 90rpx;
		color: #161513;
		width: 100%;
		height: 90rpx;
		border-bottom: 1rpx solid #E4E4E4;
	}
	.tc_input .btn{
		margin-top: 72rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}
	.tc_input .btn view{
		width: 46%;
		font-size: 28rpx;
		line-height: 72rpx;
		color: #5A9AF1;
		border: 1rpx solid #5A9AF1;
		border-radius: 46rpx;
		text-align: center;
	}
	.tc_input .btn view:last-child{
		background: #5A9AF1;
		color: #fff;
	}
</style>
