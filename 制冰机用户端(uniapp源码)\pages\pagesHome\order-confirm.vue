<template>
	<view class="container">
		<!-- 收货地址 -->
		<view class="address-section">
			<view class="address-item" @click="selectAddress" v-if="selectedAddress">
				<view class="address-icon">
					<text class="location-icon">📍</text>
				</view>
				<view class="address-content">
					<view class="address-info">
						<text class="name">{{selectedAddress.consignee}} {{selectedAddress.phone}}</text>
						<text class="arrow">›</text>
					</view>
					<view class="address-detail">{{selectedAddress.province_name + selectedAddress.city_name + selectedAddress.area_name + selectedAddress.address}}</view>
				</view>
			</view>
			<view class="address-item" @click="selectAddress" v-else>
				<view class="address-icon">
					<text class="location-icon">📍</text>
				</view>
				<view class="address-content">
					<view class="address-info">
						<text class="name" style="color: #999;">请选择收货地址</text>
						<text class="arrow">›</text>
					</view>
				</view>
			</view>
			<view class="address-divider"></view>
		</view>

		<!-- 配送时间 -->
		<view class="delivery-time">
			<view class="time-label">配送时间</view>
			<view class="time-select" @click="selectTime">
				<text v-if="selectedDeliveryTime">{{selectedDeliveryTime.date_text}} {{selectedDeliveryTime.text}}</text>
				<text v-else style="color: #999;">选择配送时间</text>
				<text class="arrow">›</text>
			</view>
		</view>

		<!-- 商品信息 -->
		<view class="goods-section">
			<view class="goods-item">
				<view class="goods-image">
					<image src="/static/ice-cube.png" mode="aspectFit"></image>
				</view>
				<view class="goods-info">
					<view class="goods-name">{{goodsInfo.name}}</view>
					<view class="goods-spec">下单规格：{{goodsInfo.spec}}</view>
					<view class="goods-price">¥ {{goodsInfo.price.toFixed(2)}}</view>
				</view>
			</view>
		</view>

		<!-- 备注 -->
		<view class="remark-section">
			<text class="remark-label">备注</text>
			<input class="remark-input" placeholder="建议留言告知先生买家冰淇淋" v-model="remark" />
		</view>

		<!-- 价格明细 -->
		<view class="price-section">
			<view class="price-item">
				<text class="price-label">商品金额</text>
				<text class="price-value">¥ {{(goodsInfo.price * goodsInfo.quantity).toFixed(2)}}</text>
			</view>
			<view class="price-item">
				<text class="price-label">配送费</text>
				<view class="delivery-fee">
					<text v-if="isCalculatingFee" style="color: #999;">计算中...</text>
					<template v-else>
						<text v-if="freeShipping" class="free-delivery">免配送费</text>
						<text v-else class="original-fee">¥ {{deliveryFee.toFixed(2)}}</text>
						<text v-if="deliveryFeeInfo && deliveryFeeInfo.distance" class="distance-info">
							(距离{{deliveryFeeInfo.distance}}km)
						</text>
					</template>
				</view>
			</view>
			<view class="price-item total">
				<text class="price-label">总金额</text>
				<text class="price-value total-price">¥ {{totalAmount.toFixed(2)}}</text>
			</view>
		</view>

		<!-- 底部提交 -->
		<view class="bottom-bar">
			<view class="total-info">
				<text class="total-count">共{{goodsInfo.quantity}}件</text>
				<text class="total-amount">合计：¥ {{totalAmount.toFixed(2)}}</text>
			</view>
			<view class="submit-btn" @click="submitOrder">
				<text>提交订单</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				remark: '',
				goodsInfo: {
					name: '冰块',
					spec: '冰块10kg',
					price: 79.00,
					quantity: 1,
					weight: 10 // 商品重量（公斤）
				},
				deliveryFee: 0,
				totalAmount: 0,
				orderData: null, // 保存从vending页面传递的订单数据
				selectedAddress: null, // 选中的收货地址
				selectedDeliveryTime: null, // 选中的配送时间
				deliveryFeeInfo: null, // 配送费详情
				isCalculatingFee: false, // 是否正在计算配送费
				freeShipping: false // 是否免配送费
			}
		},
		onLoad(options) {
			// 接收从vending页面传递的参数
			console.log('订单确认页面参数:', options)

			if (options.goodsInfo) {
				try {
					const goodsData = JSON.parse(decodeURIComponent(options.goodsInfo))
					this.goodsInfo = {
						name: goodsData.goods_name || '冰块',
						spec: goodsData.goods_spec || '冰块10kg',
						price: parseFloat(goodsData.goods_price) || 79.00,
						quantity: parseInt(goodsData.goods_num) || 1,
						weight: parseFloat(goodsData.goods_weight) || 10 // 从商品信息中获取重量
					}
					// 重新计算总金额
					this.calculateTotal()
					// 保存商品信息用于后续创建订单
					this.orderData = goodsData
				} catch (error) {
					console.error('解析商品信息失败:', error)
				}
			}

			// 获取默认收货地址
			this.getDefaultAddress()

			// 监听地址选择返回
			uni.$on('updateData', (address) => {
				this.selectedAddress = address
				// 地址变化时重新计算运费
				this.calculateDeliveryFee()
			})

			// 监听配送时间选择返回
			uni.$on('updateDeliveryTime', (deliveryTime) => {
				this.selectedDeliveryTime = deliveryTime
			})
		},
		onUnload() {
			// 移除事件监听
			uni.$off('updateData')
			uni.$off('updateDeliveryTime')
		},
		methods: {
			// 获取默认收货地址
			getDefaultAddress() {
				let that = this
				that.$api.Address({}).then(res => {
					const { code, data, msg } = res.data
					if (code === 1 && data.length > 0) {
						// 查找默认地址
						const defaultAddress = data.find(item => item.is_default == 1)
						if (defaultAddress) {
							that.selectedAddress = defaultAddress
						} else {
							// 如果没有默认地址，使用第一个地址
							that.selectedAddress = data[0]
						}
						// 获取到地址后计算运费
						that.calculateDeliveryFee()
					}
				}).catch((err) => {
					console.error('获取地址失败:', err)
				})
			},
			selectAddress() {
				// 跳转到地址选择页面
				uni.navigateTo({
					url: '/pages/pagesHome/address?type=2'
				})
			},
			selectTime() {
				// 跳转到配送时间选择页面
				let machine_no = this.orderData ? this.orderData.machine_no : ''
				uni.navigateTo({
					url: '/pages/pagesHome/delivery-time?machine_no=' + machine_no
				})
			},
			calculateTotal() {
				// 计算总金额：商品价格 * 数量 + 配送费
				const goodsTotal = this.goodsInfo.price * this.goodsInfo.quantity
				this.totalAmount = goodsTotal + this.deliveryFee
			},

			// 计算配送费用
			calculateDeliveryFee() {
				let that = this

				// 检查必要条件
				if (!that.selectedAddress || !that.orderData) {
					console.log('缺少必要条件:', {
						selectedAddress: !!that.selectedAddress,
						orderData: !!that.orderData
					})
					return
				}

				// 检查地址是否有ID
				if (!that.selectedAddress.id) {
					console.log('地址缺少ID信息')
					return
				}

				// 检查是否有机器ID
				if (!that.orderData.machine_id) {
					console.log('订单数据缺少机器ID')
					return
				}

				that.isCalculatingFee = true

				let params = {
					address_id: that.selectedAddress.id,
					machine_id: that.orderData.machine_id, // 修正：直接使用传递过来的机器ID
					total_weight: that.goodsInfo.weight, // 修正：直接使用商品重量，不再乘以数量
					type: 1 // 用户配送费
				}

				console.log('计算配送费参数:', params)

				that.$api.calculateDeliveryFee(params).then(res => {
					const { code, data, msg } = res.data
					if (code === 1) {
						that.deliveryFee = data.delivery_fee
						that.deliveryFeeInfo = data
						that.freeShipping = data.free_shipping
						// 重新计算总金额
						that.calculateTotal()
					} else {
						uni.showToast({
							icon: "none",
							title: msg,
							duration: 2000
						})
						// 计算失败时设置默认配送费
						that.deliveryFee = 10.00
						that.calculateTotal()
					}
				}).catch((err) => {
					console.error('计算配送费失败:', err)
					uni.showToast({
						icon: "none",
						title: '计算配送费失败',
						duration: 2000
					})
					// 计算失败时设置默认配送费
					that.deliveryFee = 10.00
					that.calculateTotal()
				}).finally(() => {
					that.isCalculatingFee = false
				})
			},
			submitOrder() {
				// 提交订单，跳转到支付页面
				uni.navigateTo({
					url: '/pages/pagesPay/index?type=2&order_sn=test_order_123'
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		background: #f5f5f5;
		min-height: 100vh;
	}



	.icon-back {
		font-size: 40rpx;
		color: #333;
	}

	/* 收货地址 */
	.address-section {
		background: #fff;
		margin-top: 20rpx;
	}

	.address-item {
		display: flex;
		padding: 30rpx;
		align-items: flex-start;
	}

	.address-icon {
		margin-right: 20rpx;
		margin-top: 10rpx;
	}

	.location-icon {
		font-size: 32rpx;
	}

	.address-content {
		flex: 1;
	}

	.address-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.name {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.arrow {
		font-size: 32rpx;
		color: #999;
	}

	.address-detail {
		font-size: 28rpx;
		color: #666;
		line-height: 40rpx;
	}

	.address-divider {
		height: 2rpx;
		background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
		margin: 0 30rpx;
	}

	/* 配送时间 */
	.delivery-time {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;
		padding: 30rpx;
		margin-top: 20rpx;
	}

	.time-label {
		font-size: 32rpx;
		color: #333;
	}

	.time-select {
		display: flex;
		align-items: center;
		gap: 10rpx;
		font-size: 28rpx;
		color: #999;
	}

	/* 商品信息 */
	.goods-section {
		background: #fff;
		margin-top: 20rpx;
		padding: 30rpx;
	}

	.goods-item {
		display: flex;
		align-items: center;
	}

	.goods-image {
		width: 120rpx;
		height: 120rpx;
		margin-right: 20rpx;
	}

	.goods-image image {
		width: 100%;
		height: 100%;
	}

	.goods-info {
		flex: 1;
	}

	.goods-name {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 10rpx;
	}

	.goods-spec {
		font-size: 26rpx;
		color: #999;
		margin-bottom: 15rpx;
	}

	.goods-price {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	/* 备注 */
	.remark-section {
		background: #fff;
		padding: 30rpx;
		margin-top: 20rpx;
		display: flex;
		align-items: center;
	}

	.remark-label {
		font-size: 32rpx;
		color: #333;
		margin-right: 30rpx;
		min-width: 80rpx;
	}

	.remark-input {
		flex: 1;
		font-size: 28rpx;
		color: #666;
	}

	/* 价格明细 */
	.price-section {
		background: #fff;
		margin-top: 20rpx;
		padding: 30rpx;
	}

	.price-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.price-item:last-child {
		margin-bottom: 0;
	}

	.price-label {
		font-size: 28rpx;
		color: #333;
	}

	.price-value {
		font-size: 28rpx;
		color: #333;
	}

	.delivery-fee {
		display: flex;
		align-items: center;
		gap: 10rpx;
	}

	.original-fee {
		font-size: 28rpx;
		color: #999;
		text-decoration: line-through;
	}

	.free-delivery {
		font-size: 24rpx;
		color: #ff6b6b;
	}

	.distance-info {
		font-size: 24rpx;
		color: #999;
		margin-left: 10rpx;
	}

	.total {
		border-top: 1rpx solid #f0f0f0;
		padding-top: 20rpx;
		margin-top: 20rpx;
	}

	.total-price {
		font-size: 32rpx;
		font-weight: 500;
		color: #ff6b6b;
	}

	/* 底部提交栏 */
	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #f0f0f0;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	}

	.total-info {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}

	.total-count {
		font-size: 24rpx;
		color: #999;
	}

	.total-amount {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.submit-btn {
		background: #4A90E2;
		color: #fff;
		padding: 20rpx 60rpx;
		border-radius: 50rpx;
		font-size: 32rpx;
		font-weight: 500;
	}
</style>
