<?php

namespace app\common\service\machine;

use app\admin\model\vending\Agent;
use app\admin\model\Xinyiorder;
use app\common\library\Auth;
use app\common\model\Category;
use app\common\model\Config;
use app\common\model\Coupons;
use app\common\model\vending\goods\Goods;
use app\common\model\vending\machine\MachineStatus;
use app\common\model\vending\machine\Order;
use app\common\model\vending\machine\OrderGoods;
use app\common\model\vending\machine\Type;
use app\common\model\vending\machine\Way;
use app\common\service\BaseService;
use app\common\service\shop\UserDispatchAddressService;
use app\common\service\UserExchangeCouponsService;
use app\gateway\workerman\traits\Format;
use think\Cache;
use think\Db;
use think\exception\ValidateException;
use think\Hook;
use think\Log;

/**
 * 售货机订单服务类
 */
class OrderService extends BaseService
{
    protected $model;
    use Format;

    private function getOrderSn(): string
    {
        return date('Ymd', time()) . substr(microtime(), 2, 6);
    }

    /**
     * 获取订单号后四位，按规则计算,具体计算规则需参考售货机硬件协议
     * @param $sn
     * @return float|int|mixed
     */
    private function getOrderCode($sn)
    {
        $sn = $this->encodeAscii($sn, 14);
        $sn = substr($sn, -8, 8);
        $sn = str_split($sn, 2);
        foreach ($sn as &$v) {
            $v = hexdec($v);
        }
        return (($sn[0] - 48) * 10 + ($sn[1] - 48)) * 60 + ($sn[2] - 48) * 10 + ($sn[3] - 48);
    }

    public function __construct()
    {
        $this->model = new Order();
    }

    //售货机订单创建
    public function create1($user_id, $machine, $cart_goods, $type, $platform = '')
    {
        $order_amount = 0; //订单总价
        $total_num = 0; //订单商品数量
        $discount_amount = 0;//折扣金额;
        $goods_list = [];
        $userExchangeCouponsService = new UserExchangeCouponsService();
        $flag = 0;
        foreach ($cart_goods as $v) {
            $v['num'] = max(intval($v['num']), 1);
            $way = Way::where('no', $v['way'])->where('goods_id', $v['id'])->where('machine_id', $machine['id'])->find();
//            $way->appendRelationAttr('goods',['name','image']);
            $goods = Goods::where('id', $v['id'])->find();
            $goods_price = $goods['price']; //按商品价格算
            $goods['way'] = $v['way'];//通道
//            $goods->appendRelationAttr('category',['drop_sort']);
            $goods->drop_sort = $goods->category['drop_sort'];//掉落顺序
            //检查通道库存
            if ($way['stock'] < $v['num']) {
                throw new ValidateException(sprintf("%s库存不足", $goods['name']));
            }
            //$order_amount = bcadd($order_amount, bcmul($v['num'], $way['machine_price'], 2), 2);
            $order_amount = bcadd($order_amount, bcmul($v['num'], $goods_price, 2), 2);
            $total_num = bcadd($total_num, $v['num'], 0);
            //小程序用户登录购买，安卓机触屏无需用户登录
            $goods['coupon_id'] = 0;
            $goods['discount_amount'] = 0;//折扣费用为商品价格
            $goods['num'] = $v['num'];
            if ($user_id > 0 && $type == 1 && $flag == 0) { //赠心意不允许使用兑换券
                $coupon = $userExchangeCouponsService->getCoupon($user_id, $goods['id']);
                if ($coupon) {
                    $goods['coupon_id'] = $coupon['id'];//该商品使用的兑换券
                    //$goods['discount_amount'] = $way['machine_price'];//折扣费用为当前通道商品价格
                    $goods['discount_amount'] = $goods_price;//折扣费用为当前通道商品价格
                    //$discount_amount = bcadd($discount_amount, $goods['discount_amount'], 2);// 订单折扣金额
                    $discount_amount = $goods['discount_amount'];// 订单折扣金额
                    $flag = 1;
                }

            }
            //$goods_list[] = ['id' => $v['id'], 'price' => $goods_price, 'market_price' => $goods['market_price'], 'drop_sort' => $goods['drop_sort'], 'coupon_id' => $goods['coupon_id'], 'discount_amount' => $goods['discount_amount'], 'name' => $goods['name'], 'way' => $v['way'], 'num' => $v['num'], 'image' => $goods['image']];
            $goods_list[] = $goods;
        }
        //检测购买数量是否大于售货机支持的最大购买数量

        $max_buy = (new MachineService())->maxBuy($machine['id']);
        if ($total_num > $max_buy) {
            throw new ValidateException("当前设备最大购买数量为:" . $max_buy);
        }
        $pay_amount = bcsub($order_amount, $discount_amount, 2);//实际支付金额
        //创建订单
        //订单表数据
        $order_sn = $this->getOrderSn();
        $order_code = $this->getOrderCode($order_sn);
        $order_data = [
            'order_sn' => $order_sn,
            'order_code' => $order_code,
            'agent_id' => $machine['agent_id'],
            'machine_id' => $machine['id'], //售货机
            'device_no' => $machine['device_no'], //售货机
            'warehouse_id' => $machine['warehouse_id'],//仓库
            'user_id' => $user_id,
            'total_amount' => $order_amount, //总金额，优惠前
            'total_num' => $total_num, //总金额，优惠前
            'coupon_amount' => $discount_amount,//优惠券抵用金额
            'discount_amount' => $discount_amount,//总抵用金额，售货机订单目前只有兑换券折扣，活动折扣暂时未支持
            //'user_coupon_id' => $coupons_id, //使用的优惠券 id
            'platform' => $platform,
            'pay_amount' => $pay_amount,//实际支付金额
            'type' => $type,
            'createtime' => time()
        ];
        //记录配送信息
        if ($machine['agent_id'] == 0 && $user_id > 0) {
            $userDispatchAddressService = new UserDispatchAddressService();
            $userDispatchAddressService->save($user_id, $machine);
        }
        //lock 2 秒
        $lock_key = 'user_add_order_' . $user_id;
        if (!Cache::has($lock_key)) {
            Cache::set($lock_key, '1', 2);
            return $this->transaction(function () use ($order_data, $goods_list) {
                $order = $this->model->create($order_data);
                //订单商品表数据
                $orderGoodsModel = new OrderGoods();
                $order_goods_data = [];
                foreach ($goods_list as $key => $goods) {
                    $order_goods_data[$key]['order_id'] = $order->id;
                    $order_goods_data[$key]['order_code'] = $order_data['order_code'];
                    $order_goods_data[$key]['agent_id'] = $machine['agent_id'];
                    $order_goods_data[$key]['machine_id'] = $order->machine_id;
                    $order_goods_data[$key]['device_no'] = $order_data['device_no'];
                    $order_goods_data[$key]['warehouse_id'] = $order_data['warehouse_id'];
                    $order_goods_data[$key]['way'] = $goods['way'];
                    $order_goods_data[$key]['goods_id'] = $goods['id'];
                    $order_goods_data[$key]['drop_sort'] = $goods['drop_sort'];//掉落顺序
                    $order_goods_data[$key]['goods_name'] = $goods['name'];
                    $order_goods_data[$key]['goods_num'] = $goods['num'];
                    $order_goods_data[$key]['goods_price'] = $goods['price'];
                    $order_goods_data[$key]['goods_market_price'] = $goods['market_price'];
                    $order_goods_data[$key]['coupon_id'] = $goods['coupon_id']; //券id user_exchange_coupons表主键
                    $order_goods_data[$key]['discount_amount'] = $goods['discount_amount']; //商品优惠金额
                    $order_goods_data[$key]['total_amount'] = bcmul($goods['price'], $goods['num'], 2);
                    $order_goods_data[$key]['pay_amount'] = bcsub($order_goods_data[$key]['total_amount'], $goods['discount_amount'], 2);
                    $order_goods_data[$key]['goods_image'] = $goods['image'];
                }
                $orderGoodsModel->saveAll($order_goods_data);
                $data = ['order' => $order];
                Hook::listen('machine_order_create_after', $data); //订单创建后
                return $order;
            });
        } else {
            return $order_data;
        }

    }
    public function getDistance($longitude1, $latitude1, $longitude2, $latitude2, $unit = 2, $decimal = 2)
    {

        $EARTH_RADIUS = 6370.996; // 地球半径系数
        $PI = 3.1415926;
        $radLat1 = $latitude1 * $PI / 180.0;
        $radLat2 = $latitude2 * $PI / 180.0;
        $radLng1 = $longitude1 * $PI / 180.0;
        $radLng2 = $longitude2 * $PI / 180.0;
        $a = $radLat1 - $radLat2;
        $b = $radLng1 - $radLng2;
        $distance = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2)));
        $distance = $distance * $EARTH_RADIUS * 1000;

        if ($unit == 2) {
            $distance = $distance / 1000;
        }

        return round($distance, $decimal);
    }
    protected function getCode($code,$device_no,$spec_num,$category_id,$order_sn)
    {
//'{
//            "DEVICE":"1234567890",
//            "CMD":2,
//            "WEIGHT":1,
//            "TYPE":1,
//            "SEQ":"000XXXX"
//        }';

        $data =json_encode([
            'DEVICE'=>$device_no,
            'WEIGHT'=>$spec_num,
            'TYPE'=>$category_id,
            'SEQ'=>$order_sn
        ]);
        // 加密密钥，长度必须是 16、24 或 32 字节
        $key = '08f46334bfa3f71783890006655e4dc6';
        // 初始化向量，AES-128 模式下长度为 16 字节

        $iv = '4a1586659a32ba62';

        // 加密数据
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
        $params['text'] = base64_encode($encrypted);
        $mimetype = 'png'  ;
        $qrCode = \addons\qrcode\library\Service::qrcode($params);
        // 写入到文件
        if (1) {

            $qrcodePath = ROOT_PATH .  'public/qrcode/order/';
            if (!is_dir($qrcodePath)) {
                @mkdir($qrcodePath);
            }
            if (is_really_writable($qrcodePath)) {
                $filePath = $qrcodePath . $code . '.png' ;

                $qrCode->writeFile($filePath);
            }
        }
    }
    public function refund($id)
    {
        $order = $this->model->with('goods')->where('id', $id)->find();

        $item = \app\admin\model\vending\machine\order\Goods::where('order_id', $id)->find();

        if($item['refund_status'] == '2'){
            $this->error('订单已退款');
        }
        $refund_money = $item['pay_amount'];
        if ($refund_money <= 0) {
            $this->error('退款金额不能小于 0');
        }

        if($order['type'] == 2){
            $this->error('送心意订单不支持退款');
        }
        Db::transaction(function () use ($order, $item, $refund_money) {
            // 单个商品退款
            \app\admin\model\vending\machine\Order::startRefund($order, $item, $refund_money, [], '用户申请退款');

        });

        $this->model->with('goods')->where('id', $id)->update([
            'status'=>4
        ]);
        return [];
    }

    /**
     * 计算配送费用
     * @param int $address_id 地址ID
     * @param int $machine_id 机器ID
     * @param float $total_weight 总重量
     * @return array
     */
    private function calculateDeliveryFee($address_id, $machine_id, $total_weight)
    {
        try {
            // 获取收货地址信息
            $address = \app\admin\model\UserAddress::get($address_id);
            if (!$address || !$address->latitude || !$address->longitude) {
                return ['delivery_fee' => 0, 'rider_fee' => 0];
            }

            // 获取机器信息
            $machine = \app\admin\model\vending\Machine::get($machine_id);
            if (!$machine || !$machine->lat || !$machine->lon) {
                return ['delivery_fee' => 0, 'rider_fee' => 0];
            }

            // 计算距离（公里）
            $distance = $this->calculateDistance($machine->lat, $machine->lon, $address->latitude, $address->longitude);

            // 获取配送费模板
            $template_id = $machine->customer_dispatch_template_id;
            if (!$template_id) {
                return ['delivery_fee' => 0, 'rider_fee' => 0];
            }

            $template = \app\admin\model\DispatchFeeTemplate::get($template_id);
            if (!$template || $template->status != 1) {
                return ['delivery_fee' => 0, 'rider_fee' => 0];
            }

            // 计算配送费
            $feeResult = $template->calculateDeliveryFee($distance, $total_weight);
            if (!$feeResult['can_deliver']) {
                throw new ValidateException($feeResult['message']);
            }

            return [
                'delivery_fee' => $feeResult['fee'],
                'rider_fee' => $feeResult['fee'] * 0.8, // 假设骑手佣金为配送费的80%
                'distance' => $distance
            ];
        } catch (\Exception $e) {
            // 配送费计算失败时返回默认值
            return ['delivery_fee' => 0, 'rider_fee' => 0];
        }
    }

    /**
     * 计算两点间距离（公里）
     * @param float $lat1 纬度1
     * @param float $lng1 经度1
     * @param float $lat2 纬度2
     * @param float $lng2 经度2
     * @return float
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371; // 地球半径（公里）

        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        $deltaLat = $lat2 - $lat1;
        $deltaLng = $lng2 - $lng1;

        $a = sin($deltaLat / 2) * sin($deltaLat / 2) + cos($lat1) * cos($lat2) * sin($deltaLng / 2) * sin($deltaLng / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    //售货机订单创建
    public function create($user_id, $machine, $goods_id, $goods_num, $platform = '', $coupon_id = 0, $order_mode = 1, $user_address_id = 0, $delivery_time = [])
    {
        $order_amount = 0; //订单总价
        $total_num = 0; //订单商品数量
        $discount_amount = 0;//折扣金额;
        $goods_list = [];
        $userExchangeCouponsService = new UserExchangeCouponsService();

        $coupons = Coupons::get($coupon_id);


            $goods = Goods::where('id', $goods_id)->find();
            $machine_type_spec_num = Type::where(['id'=>$machine['type_id']])->value('single');



            if($goods['spec_num']==0 && $goods_num%$machine_type_spec_num!=0){
                throw new ValidateException(sprintf("规格需为%s整数倍", $machine_type_spec_num));
            }

            $machine_status = MachineStatus::where([
                'device_no'=>$machine['device_no']
            ])->find();

            if($machine_status['status'] != 0&&$machine_status['net_status'] != 1 ){
                throw new ValidateException('机器状态异常');

            }

            $goods_price = round($goods['price']*$machine['machine_discount']/100,2); //按通道设定价格

            $goods['pay_price'] = $goods_price;
//            $goods->appendRelationAttr('category',['drop_sort']);
            $goods->drop_sort = 0;//掉落顺序
//            if ($type == 1) {
//                //检查通道库存
//                if ($way['stock'] < $v['num']) {
//                    throw new ValidateException(sprintf("%s库存不足", $goods['name']));
//                }
//            }

            //$order_amount = bcadd($order_amount, bcmul($v['num'], $way['machine_price'], 2), 2);
            $order_amount = bcadd($order_amount, bcmul($goods_num, $goods_price, 2), 2);

            p($goods_num);
            pd($goods_price);
            $total_num = bcadd($total_num, $goods_num, 0);
            //小程序用户登录购买，安卓机触屏无需用户登录
            $goods['coupon_id'] = 0;
            $goods['exchange_coupon_ids'] = '';
            $goods['discount_amount'] = 0;//折扣费用为商品价格
            $goods['num'] = $goods_num;


        $goods_list[]=$goods;

        if ($user_id > 0  && $coupons) {
            if($coupons['type']=='3'){
                $discount_amount = round($order_amount-round($coupons['discount'] * $order_amount/100,2),2);
            }else{
                $discount_amount = $coupons['amount'];

            }
        }


//        $pay_amount = bcsub($order_amount, $discount_amount, 2);//实际支付金额
        // 处理外卖模式的配送费
        $dispatch_fee = 0;
        $rider_fee = 0;
        $delivery_address_info = [];

        if ($order_mode == 2 && $user_address_id > 0) {
            // 获取用户地址信息
            $userAddress = \app\admin\model\UserAddress::get($user_address_id);
            if (!$userAddress) {
                throw new ValidateException('收货地址不存在');
            }

            // 计算配送费 - 需要传递实际重量而不是goods_num
            // 对于固定规格商品，goods_num=1，需要使用商品的实际重量
            // 对于自定义规格商品，goods_num就是重量值
            $actualWeight = $goods_num;
            if ($goods_num == 1) {
                // 固定规格商品，从商品信息中获取重量
                $goods = \app\common\model\vending\goods\Goods::get($goods_id);
                if ($goods && $goods->weight > 0) {
                    $actualWeight = $goods->weight;
                }
            }
            $deliveryFeeResult = $this->calculateDeliveryFee($user_address_id, $machine['id'], $actualWeight);
            $dispatch_fee = $deliveryFeeResult['delivery_fee'] ?? 0;
            $rider_fee = $deliveryFeeResult['rider_fee'] ?? 0;

            // 保存地址信息
            $delivery_address_info = [
                'delivery_consignee' => $userAddress->consignee,
                'delivery_mobile' => $userAddress->phone,
                'delivery_address' => $userAddress->province_name . $userAddress->city_name . $userAddress->area_name . $userAddress->address,
            ];
        }

        $pay_amount = max(bcsub(bcadd($order_amount, $dispatch_fee, 2), $discount_amount, 2), 0);//实际支付金额（包含配送费）
        //创建订单
        //订单表数据
        $order_sn = $this->getOrderSn();
        $order_code = $this->getOrderCode($order_sn);

        $order_data = [
            'order_sn' => $order_sn,
            'order_code' => $order_code,
            'agent_id' => $machine['agent_id'],
            'machine_id' => $machine['id'], //售货机
            'device_no' => $machine['device_no'], //售货机
            'warehouse_id' => $machine['warehouse_id'],//仓库
            'user_id' => $user_id,
            'total_amount' => bcadd($order_amount, $dispatch_fee, 2), //总金额（包含配送费）
            'goods_amount' => $order_amount, //商品总价
            'total_num' => $total_num, //总数量
            'coupon_amount' => $discount_amount,//优惠券抵用金额
            'discount_amount' => $discount_amount,//总抵用金额，售货机订单目前只有兑换券折扣，活动折扣暂时未支持
            //'user_coupon_id' => $coupons_id, //使用的优惠券 id
            'platform' => $platform,
            'pay_amount' => $pay_amount,//实际支付金额
            'type' => 1,
            'order_mode' => $order_mode, //订单模式:1=自取,2=外卖
            'user_address_id' => $user_address_id, //用户地址ID
            'dispatch_fee' => $dispatch_fee, //配送费
            'rider_fee' => $rider_fee, //骑手佣金
            'dispatch_type' => 1, //配送类型:1=立即配送,2=预约配送
            'createtime' => time()
        ];
//pd($order_data);
        // 合并地址信息
        $order_data = array_merge($order_data, $delivery_address_info);

        // 处理配送时间
        if ($order_mode == 2 && !empty($delivery_time)) {
            if (isset($delivery_time['start_time'])) {
                $order_data['expected_delivery_starttime'] = strtotime($delivery_time['start_time']);
            }
            if (isset($delivery_time['end_time'])) {
                $order_data['expected_delivery_endtime'] = strtotime($delivery_time['end_time']);
            }
            if (isset($delivery_time['type']) && $delivery_time['type'] == 2) {
                $order_data['dispatch_type'] = 2; // 预约配送
            }
        }
        //记录配送信息
        if ($machine['agent_id'] == 0 && $user_id > 0) {
            $userDispatchAddressService = new UserDispatchAddressService();
            $userDispatchAddressService->save($user_id, $machine);
        }



        //lock 2 秒
        $lock_key = 'user_add_order_' . $user_id;
        if (!Cache::has($lock_key)) {
            Cache::set($lock_key, '1', 2);
            return $this->transaction(function () use ($order_data, $goods_list,$goods_num) {
                $order = $this->model->create($order_data);

                $this->getCode($order->id,$order['device_no'],$goods_list[0]['spec_num']==0?$goods_num:$goods_list[0]['spec_num'],$goods_list[0]['category_id'],$order['order_sn']);
                //订单商品表数据
                $orderGoodsModel = new OrderGoods();
                $order_goods_data = [];
                foreach ($goods_list as $key => $goods) {
                    $order_goods_data[$key]['order_id'] = $order->id;
                    $order_goods_data[$key]['order_code'] = $order_data['order_code'];
                    $order_goods_data[$key]['machine_id'] = $order->machine_id;
                    $order_goods_data[$key]['device_no'] = $order_data['device_no'];
                    $order_goods_data[$key]['warehouse_id'] = $order_data['warehouse_id'];
//                    $order_goods_data[$key]['way'] = $goods['way'];
                    $order_goods_data[$key]['way'] = 1;
                    $order_goods_data[$key]['goods_id'] = $goods['id'];
                    $order_goods_data[$key]['goods_category_id'] = $goods['category_id'];
                    $order_goods_data[$key]['drop_sort'] = $goods['drop_sort'];//掉落顺序
                    $order_goods_data[$key]['goods_name'] = $goods['name'];
                    $order_goods_data[$key]['goods_num'] = $goods['num'];
                    $order_goods_data[$key]['goods_price'] = $goods['pay_price'];
                    $order_goods_data[$key]['goods_market_price'] = $goods['market_price'];
                    $order_goods_data[$key]['goods_purchase_price'] = $goods['purchase_price'];
                    $order_goods_data[$key]['coupon_id'] = $goods['coupon_id']; //券id user_exchange_coupons表主键
                    $order_goods_data[$key]['exchange_coupon_ids'] = $goods['exchange_coupon_ids']; //券id user_exchange_coupons表主键
                    $order_goods_data[$key]['discount_amount'] = $goods['discount_amount']; //商品优惠金额
                    $order_goods_data[$key]['total_amount'] = bcmul($goods['pay_price'], $goods['num'], 2);
                    $order_goods_data[$key]['pay_amount'] = max(bcsub($order_goods_data[$key]['total_amount'], $goods['discount_amount'], 2),0);
                    $order_goods_data[$key]['goods_image'] = $goods['image'];
                    $order_goods_data[$key]['type'] = $order_data['type'];
                    $order_goods_data[$key]['user_id'] = $order_data['user_id'];
                }
                $orderGoodsModel->saveAll($order_goods_data);
                $data = ['order' => $order];
                Hook::listen('machine_order_create_after', $data); //订单创建后
                return $order;
            });
        } else {
            return $order_data;
        }

    }

    //售货柜订单列表
    public function list($params, $field = "*")
    {
//        $where ['status'] = ['in', '1,2,4'];
        $where['user_id'] = Auth::instance()->id;
//        $where['user_id'] = 1;
//        $where = ['user_id','=',1];
        $page = 1;
        $limit = 10;
        $order = 'id desc';
//        isset($params['status']) && !empty($params['status']) && $where['order.status'] = $params['status'];
        isset($params['page']) && !empty($params['page']) && ($params['page'] != -1) && $page = $params['page'];
        isset($params['limit']) && !empty($params['limit']) && ($params['limit'] != -1) && $limit = $params['limit'];
        if(isset($params['type'])&&$params['type']){
            $where ['status'] = $params['type'];
        }
        $total = $this->model->where($where)->count();
        $list = $this->model->with(['goods' => function ($query) {
//            $query->withField('goods_name,goods_price,goods_num');
        }])->where($where)->limit($limit)->order($order)->page($page)->select();
       foreach ($list as $value){
           foreach ($value->goods as $item){
              $category_id=\app\admin\model\vending\Goods::where('id',$item->goods_id)->value('category_id');
              $item['category_name'] = \app\common\model\vending\goods\Category::where('id',$category_id)->value('name');
           }

       }
        return compact('total', 'list');
    }


    public function detail($id)
    {
        $order = $this->model->with('goods')->where('id', $id)->find();
        if($order['type']=='2'){

            $order['xinyi'] = (new Xinyiorder)::with('user')->where([
                'order_id'=>$order['id']
            ])->find();

        }

        foreach ($order->goods as $item){
            $category_id=\app\admin\model\vending\Goods::where('id',$item->goods_id)->value('category_id');
            $item['category_name'] = \app\common\model\vending\goods\Category::where('id',$category_id)->value('name');
        }
        $order['qrcode'] = 'http://'.$_SERVER['HTTP_HOST'].'/qrcode/order/'.$id.'.png';
        $order['xuzhi'] =Config::where(['id'=>25])->value('value');

        return $order;
    }

    public function outGoodsFail($order, $item, $fail_num)
    {
        $refund_amount = 0; //待退款金额
        if ($item['exchange_coupon_ids']) {
            $exchange_coupon_ids = explode(',', $item['exchange_coupon_ids']);
            $exchange_coupon_count = count($exchange_coupon_ids);
            if ($exchange_coupon_count >= $fail_num) {
                $exchange_coupon_ids = array_slice($exchange_coupon_ids, 0, $fail_num);
            } else {
                $refund_amount = bcmul($item['goods_price'], bcsub($fail_num, $exchange_coupon_count, 0), 2);
            }
            //推优惠券
            $userExchangeCouponsService = new UserExchangeCouponsService();
            $userExchangeCouponsService->back($exchange_coupon_ids);
        } else {
            // $refund_amount = bcmul($item['goods_price'], $fail_num, 2);
            $refund_amount = round(bcmul($item['goods_price'], $fail_num, 2),2);
            \app\admin\model\vending\machine\Order::startRefund($order, $item, $refund_amount, null, '出货失败退款');
        }
    }

}