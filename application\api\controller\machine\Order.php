<?php

namespace app\api\controller\machine;

use app\admin\controller\vending\machine\order\Goods;
use app\admin\model\Advertisement;
use app\admin\model\vending\Agent;
use app\common\model\Ad;
use app\common\model\Config;
use app\common\model\ScoreLog;
use app\common\model\UserCoupons;
use app\common\service\machine\OrderService;

/**
 * 小程序售货机订单接口
 */
class Order extends Base
{
    protected function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 订单创建
     *  {"goods_list":[{"id":621,"num":2,"way":1},{"id":2,"num":3,"way":2}],"type":1}
     * @return void
     */
    public function create()
    {
        [$goods_id, $goods_num,$coupon_id] = $this->params([
            ['goods_id', 0],
            ['goods_num', 1],
            ['coupon_id', 0], //订单可用优惠券
        ]);


        $this->service = new OrderService();
        $this->success('', $this->service->create($this->auth->id, $this->machine, $goods_id, $goods_num,$this->platform,$coupon_id));
    }
    /**
     * 绑定订单
     * @return void
     */
    public function bind()
    {
        [$order_sn] = $this->params([
            ['order_sn', ''],
        ]);

        $order = \app\admin\model\vending\machine\Order::where([
            'order_sn'=>$order_sn
        ])->find();
        \app\admin\model\vending\machine\Order::where([
            'order_sn'=>$order_sn
        ])->update([
            'user_id'=>$this->auth->id
        ]);
        \app\admin\model\vending\machine\order\Goods::where([
            'order_id'=>$order['id']
        ])->update([
            'user_id'=>$this->auth->id
        ]);
        $this->success('ok');
    }
    //支付成功后拉取，赠送的优惠券和积分，已废弃，统一走common/payAfter
    public function payAfter()
    {
        [$order_sn] = $this->params([
            ['order_sn', '']
        ]);
        empty($order_sn) && $this->error('订单号为空');
        $coupon = UserCoupons::where('item_id',$order_sn)->with('coupons')->find();
        if($coupon){
            $coupon = $coupon->coupons;
            if ($coupon->use_time_limit == 1) {
                $coupon->end_time = date('Y.m.d', $coupon['usetimeend']);
            } else {
                $coupon->end_time = '长期有效';
            }
        }
        $score =  ScoreLog::where('item_id',$order_sn)->value('score');
        $this->success('',compact('coupon','score'));
    }

    //支付成功广告
    public function payAd()
    {
        //获取当前机器支付成功广告
        $ad = Ad::field('id,mime,image,start_time,end_time')->where('machine_id', $this->machine->id)->where('type', 2)
            ->where('start_time', '<= time', time())
            ->where('end_time', '>= time', time())
            ->limit(2)->select();
        $this->success('', $ad);
    }


    /**
     * 计算配送费用
     * @return void
     */
    public function calculateDeliveryFee()
    {
        [$address_id, $machine_id, $total_weight, $type] = $this->params([
            ['address_id', 0],
            ['machine_id', 0],
            ['total_weight', 0],
            ['type', 1] // 1=用户配送费，2=外卖员配送费
        ]);

        if (!$address_id || !$machine_id || !$total_weight) {
            $this->error('参数不完整');
        }


            // 获取收货地址信息
            $address = \app\admin\model\UserAddress::get($address_id);
            if (!$address) {
                $this->error('收货地址不存在');
            }

            // 检查地址是否有经纬度信息
            if (!$address->latitude || !$address->longitude) {
                $this->error('收货地址缺少经纬度信息，请重新编辑地址');
            }

            // 获取机器信息
            $machine = \app\admin\model\vending\Machine::get($machine_id);
            if (!$machine) {
                $this->error('机器不存在');
            }

            // 检查机器是否有经纬度信息
            if (!$machine->lat || !$machine->lon) {
                $this->error('机器位置信息不完整');
            }

            // 获取配送费模板ID
            $template_id = $type == 1 ? $machine->customer_dispatch_template_id : $machine->rider_dispatch_template_id;
            if (!$template_id) {
                $this->error('未配置配送费模板');
            }

            // 获取配送费模板
            $template = \app\admin\model\DispatchFeeTemplate::get($template_id);
            if (!$template || $template->status != 1) {
                $this->error('配送费模板不存在或已禁用');
            }

            // 解析配送规则
            $rules = json_decode($template->rules, true);
            if (!$rules) {
                $this->error('配送规则格式错误');
            }

            // 计算距离
            $distance = $this->calculateDistance($address->latitude, $address->longitude, $machine->lat, $machine->lon);

            // 检查配送范围
            if (isset($rules['limit_rules']['max_delivery_km']) && $distance > $rules['limit_rules']['max_delivery_km']) {
                $this->error('超出配送范围，最大配送距离：' . $rules['limit_rules']['max_delivery_km'] . '公里');
            }

            // 检查起送重量
            if (isset($rules['weight_rules']['min_kg_to_ship']) && $total_weight < $rules['weight_rules']['min_kg_to_ship']) {
                $this->error('未达到起送重量，最低起送：' . $rules['weight_rules']['min_kg_to_ship'] . '公斤');
            }

            // 检查是否免配送费
            if (isset($rules['weight_rules']['free_shipping_kg']) && $total_weight >= $rules['weight_rules']['free_shipping_kg']) {
                $this->success('计算成功', [
                    'delivery_fee' => 0,
                    'distance' => round($distance, 2),
                    'free_shipping' => true,
                    'free_shipping_reason' => '满' . $rules['weight_rules']['free_shipping_kg'] . '公斤免配送费'
                ]);
                return;
            }

            // 计算配送费
            $delivery_fee = $this->calculateFee($distance, $rules['fee_rules']);

            $this->success('计算成功', [
                'delivery_fee' => $delivery_fee,
                'distance' => round($distance, 2),
                'free_shipping' => false,
                'fee_breakdown' => $this->getFeeBreakdown($distance, $rules['fee_rules'])
            ]);


    }

    /**
     * 计算两点间距离（公里）
     * 使用Haversine公式
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earth_radius = 6371; // 地球半径（公里）

        $lat1_rad = deg2rad($lat1);
        $lon1_rad = deg2rad($lon1);
        $lat2_rad = deg2rad($lat2);
        $lon2_rad = deg2rad($lon2);

        $delta_lat = $lat2_rad - $lat1_rad;
        $delta_lon = $lon2_rad - $lon1_rad;

        $a = sin($delta_lat / 2) * sin($delta_lat / 2) +
             cos($lat1_rad) * cos($lat2_rad) *
             sin($delta_lon / 2) * sin($delta_lon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earth_radius * $c;

        return $distance;
    }

    /**
     * 根据距离和费用规则计算配送费
     */
    private function calculateFee($distance, $fee_rules)
    {
        $base_km = $fee_rules['base_km'] ?? 0;
        $base_fee = $fee_rules['base_fee'] ?? 0;
        $over_km_fee = $fee_rules['over_km_fee'] ?? 0;

        if ($distance <= $base_km) {
            return $base_fee;
        } else {
            $over_distance = $distance - $base_km;
            return $base_fee + ($over_distance * $over_km_fee);
        }
    }

    /**
     * 获取费用明细
     */
    private function getFeeBreakdown($distance, $fee_rules)
    {
        $base_km = $fee_rules['base_km'] ?? 0;
        $base_fee = $fee_rules['base_fee'] ?? 0;
        $over_km_fee = $fee_rules['over_km_fee'] ?? 0;

        $breakdown = [
            'base_km' => $base_km,
            'base_fee' => $base_fee,
            'distance' => round($distance, 2)
        ];

        if ($distance > $base_km) {
            $over_distance = $distance - $base_km;
            $breakdown['over_distance'] = round($over_distance, 2);
            $breakdown['over_km_fee'] = $over_km_fee;
            $breakdown['over_fee'] = round($over_distance * $over_km_fee, 2);
        }

        return $breakdown;
    }

    /**
     * 获取配送时间选项
     * @return void
     */
    public function deliveryTime()
    {
        // 获取配送时间配置
        $timeConfig = config('time');

        $result = [];
        $currentTime = time();
        $currentDate = date('Y-m-d');

        // 根据配置模式生成时间选项
        if ($timeConfig['mode'] === 'daily') {
            // 每日重复模式
            $dailyTime = $timeConfig['daily_time']; // 格式: '09:00-22:00'
            list($startTime, $endTime) = explode('-', $dailyTime);

            // 生成可预订的天数
            for ($day = 0; $day < $timeConfig['slot']['advance_booking_days']; $day++) {
                $targetDate = date('Y-m-d', strtotime("+{$day} days"));
                $dateSlots = $this->generateTimeSlots(
                    $targetDate,
                    $startTime,
                    $endTime,
                    $timeConfig['slot']['interval_minutes'],
                    $timeConfig['slot']['preparation_minutes'],
                    $currentTime
                );

                if (!empty($dateSlots)) {
                    $result[] = [
                        'date' => $targetDate,
                        'date_text' => $this->formatDateText($targetDate),
                        'slots' => $dateSlots
                    ];
                }
            }
        } elseif ($timeConfig['mode'] === 'daterange') {
            // 指定日期范围模式
            $daterangeTime = $timeConfig['daterange_time']; // 格式: '2025-10-01 08:00:00 - 2025-10-07 23:00:00'
            list($startDateTime, $endDateTime) = explode(' - ', $daterangeTime);

            $startTimestamp = strtotime($startDateTime);
            $endTimestamp = strtotime($endDateTime);

            // 只在指定的日期范围内生成时间选项
            if ($currentTime >= $startTimestamp && $currentTime <= $endTimestamp) {
                $startDate = date('Y-m-d', $startTimestamp);
                $endDate = date('Y-m-d', $endTimestamp);
                $startTime = date('H:i', $startTimestamp);
                $endTime = date('H:i', $endTimestamp);

                $currentDate = date('Y-m-d', max($currentTime, $startTimestamp));
                $targetDate = $currentDate;

                while ($targetDate <= $endDate) {
                    $dateSlots = $this->generateTimeSlots(
                        $targetDate,
                        $startTime,
                        $endTime,
                        $timeConfig['slot']['interval_minutes'],
                        $timeConfig['slot']['preparation_minutes'],
                        $currentTime
                    );

                    if (!empty($dateSlots)) {
                        $result[] = [
                            'date' => $targetDate,
                            'date_text' => $this->formatDateText($targetDate),
                            'slots' => $dateSlots
                        ];
                    }

                    $targetDate = date('Y-m-d', strtotime($targetDate . ' +1 day'));
                }
            }
        }

        $this->success('配送时间选项', $result);
    }

    /**
     * 生成时间段选项
     */
    private function generateTimeSlots($date, $startTime, $endTime, $intervalMinutes, $preparationMinutes, $currentTime)
    {
        $slots = [];
        $startTimestamp = strtotime($date . ' ' . $startTime);
        $endTimestamp = strtotime($date . ' ' . $endTime);
        $minBookingTime = $currentTime + ($preparationMinutes * 60); // 最早可预订时间

        $currentSlotTime = $startTimestamp;

        while ($currentSlotTime < $endTimestamp) {
            $slotEndTime = $currentSlotTime + ($intervalMinutes * 60);

            // 检查时间段是否在可预订范围内
            if ($slotEndTime > $minBookingTime) {
                $slots[] = [
                    'start_time' => date('H:i', $currentSlotTime),
                    'end_time' => date('H:i', $slotEndTime),
                    'text' => date('H:i', $currentSlotTime) . '-' . date('H:i', $slotEndTime),
                    'timestamp' => $currentSlotTime
                ];
            }

            $currentSlotTime = $slotEndTime;
        }

        return $slots;
    }

    /**
     * 格式化日期文本
     */
    private function formatDateText($date)
    {
        $today = date('Y-m-d');
        $tomorrow = date('Y-m-d', strtotime('+1 day'));

        if ($date === $today) {
            return '今天 ' . date('m月d日', strtotime($date));
        } elseif ($date === $tomorrow) {
            return '明天 ' . date('m月d日', strtotime($date));
        } else {
            $weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            $weekday = $weekdays[date('w', strtotime($date))];
            return '周' . $weekday . ' ' . date('m月d日', strtotime($date));
        }
    }

    //广告
    public function ad()
    {

        $min_view=Config::where('id',27)->value('value');
        if($this->machine->agent_id){

            $agent=Agent::get($this->machine->agent_id);

            $min_view=$agent->min_view;

        }


        //获取当前机器支付成功广告
        $list = Advertisement::where('status', 1)->column('video');

        $count=count($list);

        $ad='';
        if($count>0){
            $key=mt_rand(0,$count-1);
            $ad=$list[$key];
        }


        if (stripos($ad, "https://") === FALSE) {
            $ad='https://zbj.sdbaocheng.com/'.$ad;
        }
        $this->success('', [
            'ad'=>$ad,
            'min_view'=>$min_view
        ]);
    }

}
