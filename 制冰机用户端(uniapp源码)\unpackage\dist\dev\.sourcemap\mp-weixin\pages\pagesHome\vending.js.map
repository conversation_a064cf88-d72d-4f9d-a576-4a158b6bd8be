{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/vending.vue?7e95", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/vending.vue?37cd", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/vending.vue?8b9b", "uni-app:///pages/pagesHome/vending.vue", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/vending.vue?00c8", "webpack:///D:/phpEnv/www/2025/制冰机(李宁项目)/制冰机用户端(uniapp源码)/pages/pagesHome/vending.vue?23c5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "api", "kefu_tel", "ps_tel", "machine_no", "machine_info", "banner", "way", "notice", "goods_id", "goods_num", "price", "total", "delivery_type", "isBtn", "onLoad", "onShow", "watch", "onShareAppMessage", "console", "path", "methods", "getPstel", "that", "res", "code", "msg", "time", "uni", "icon", "title", "duration", "catch", "getTel", "getInfo", "setDeliveryType", "setGoods", "getClose", "getSubmit", "actualWeight", "actualQuantity", "machine_id", "goods_name", "goods_price", "goods_spec", "goods_weight", "url", "getCall", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAutB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0E3uB;EACAC;IACA;MACAC;MAAA;;MAEAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MAAA;;MAEAC;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;MACA;MACA;IACA;IACA;MACA;IACA;IACA;IACA;IACA;EACA;EACAC,2BAEA;EACAC;IACAP;MACA;QACA;MACA;IACA;EACA;EACAQ;IACA;MACAC;IACA;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;QACA,gBAKAC;UAJAC;UACAzB;UACA0B;UACAC;QAEA;UACAJ;QACA;UACAK;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACAC;MACA;MACA;MACAV;QACA,iBAKAC;UAJAC;UACAzB;UACA0B;UACAC;QAEA;UACAJ;QACA;UACAK;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAE;MACA;MACA;QACA;MACA;MACAX;QACA,iBAKAC;UAJAC;UACAzB;UACA0B;UACAC;QAEA;UACAJ;UACAA;UACAA;UACAA;QACA;UACAK;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MACA;MACAb;MACA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;QACAA;MACA;IACA;IACAc;MACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;QACAf;QACA;MACA;MACA;QACAK;UACAC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UAAA;QAAA;;QAEA;QACA;QACA;;QAEA;QACA;UACAQ;UACAC;QACA;QAEA;UACApC;UACAqC;UAAA;UACAhC;UACAC;UAAA;UACAgC;UACAC;UACAC;UACAC;QACA;;QACAjB;UACAkB;QACA;QACA;MACA;;MAEA;MACAvB;MACA;QACA;QACAd;QACAC;MACA;MACAa;QACA,iBAKAC;UAJAC;UACAzB;UACA0B;UACAC;QAEA;UACAJ;UACA;UACAK;YACAkB;UACA;QACA;UACAvB;UACAK;YACAC;YACAC;YACAC;UACA;QACA;MACA,GACAC,sBAEA;IACA;IACA;IACAe;MACA;MACAnB;QACAoB;MACA,yBAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3UA;AAAA;AAAA;AAAA;AAAwhC,CAAgB,w8BAAG,EAAC,C;;;;;;;;;;;ACA5iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pagesHome/vending.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pagesHome/vending.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./vending.vue?vue&type=template&id=245ee228&\"\nvar renderjs\nimport script from \"./vending.vue?vue&type=script&lang=js&\"\nexport * from \"./vending.vue?vue&type=script&lang=js&\"\nimport style0 from \"./vending.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pagesHome/vending.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vending.vue?vue&type=template&id=245ee228&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.way.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vending.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vending.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"banner_bg\">\r\n\t\t\t<swiper autoplay='true' circular=\"true\" interval='5000' duration='500' class='banner'>\r\n\t\t\t\t<swiper-item class=\"item_image\" v-for=\"(item,index) in banner\">\r\n\t\t\t\t\t<image :src='api + item.image' class='swiper_image' mode=\"aspectFill\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<view class=\"nr\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"xx\">\r\n\t\t\t\t\t<view>{{notice.title}}</view>\r\n\t\t\t\t\t<view>{{notice.content}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"text\" @click=\"getCall(1)\">配送电话：{{ps_tel}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"specification\">\r\n\t\t\t\t<!-- 购买方式选择 -->\r\n\t\t\t\t<view class=\"delivery-type\">\r\n\t\t\t\t\t<view class=\"delivery-buttons\">\r\n\t\t\t\t\t\t<view :class=\"delivery_type == 1?'delivery-btn active':'delivery-btn'\" @click=\"setDeliveryType(1)\">\r\n\t\t\t\t\t\t\t<text>自提</text>\r\n\t\t\t\t\t\t\t<text class=\"distance\">距离2.7km</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view :class=\"delivery_type == 2?'delivery-btn active':'delivery-btn'\" @click=\"setDeliveryType(2)\">\r\n\t\t\t\t\t\t\t<text>外卖</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"bt\">规格选择</view>\r\n\t\t\t\t<view class=\"list\" v-if=\"way.length > 0\">\r\n\t\t\t\t\t<view :class=\"item.id == goods_id?'xx on':'xx'\" v-for=\"item in way\" @click=\"setGoods(item)\">\r\n\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t<text>￥{{item.spec_num > 0?item.price:item.price + '/' + item.spec}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn\" @click=\"getSubmit\">确认订单 {{total?'￥' + total:''}}</view>\r\n\t\t\t\t<!-- <view class=\"btn\" v-else @click=\"getScan\">扫码下单</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"message\">\r\n\t\t\t<view class=\"xx\" @click=\"getCall(2)\">\r\n\t\t\t\t<image src=\"/static/icon_my7.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>在线客服</view>\r\n\t\t\t\t\t<view>24小时在线守护</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<navigator url=\"/pages/pagesMy/message?type=1\" hover-class=\"none\" class=\"xx\">\r\n\t\t\t\t<image src=\"/static/icon_my6.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t<view>意见反馈</view>\r\n\t\t\t\t\t<view>24让我们更懂您</view>\r\n\t\t\t\t</view>\r\n\t\t\t</navigator>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<uni-popup ref=\"showModal\" type=\"center\" :mask-click=\"false\">\r\n\t\t\t<view class=\"tc_input\">\r\n\t\t\t\t<view class=\"title\">自定义</view>\r\n\t\t\t\t<input type=\"number\" placeholder=\"请输入重量(KG)\" v-model=\"goods_num\">\r\n\t\t\t\t<view class=\"btn\">\r\n\t\t\t\t\t<view @click=\"getClose(1)\">取消</view>\r\n\t\t\t\t\t<view @click=\"getClose(2)\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '', //域名\r\n\r\n\t\t\t\tkefu_tel: '',\r\n\t\t\t\tps_tel: '',\r\n\t\t\t\tmachine_no: '',\r\n\t\t\t\tmachine_info: null, // 机器完整信息\r\n\t\t\t\tbanner: [],\r\n\t\t\t\tway: [],// 规格\r\n\t\t\t\tnotice: '',\r\n\r\n\t\t\t\tgoods_id: '',\r\n\t\t\t\tgoods_num: '',\r\n\t\t\t\tprice: '',\r\n\t\t\t\ttotal: '',\r\n\r\n\t\t\t\tdelivery_type: 1, // 购买方式 1:自提 2:外卖\r\n\r\n\t\t\t\tisBtn: false,\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.api = this.$http.apiPic\r\n\t\t\t// 扫码进入\r\n\t\t\tif (option.q) {\r\n\t\t\t\tlet qrUrl = decodeURIComponent(option.q)\r\n\t\t\t\tlet machine_no = this.$util.getQueryString(qrUrl, 'machine-no')\r\n\t\t\t\tthis.machine_no = machine_no\r\n\t\t\t}\r\n\t\t\tif (option.machine_no) {\r\n\t\t\t\tthis.machine_no = option.machine_no\r\n\t\t\t}\r\n\t\t\tthis.getTel()\r\n\t\t\tthis.getInfo()\r\n\t\t\tthis.getPstel()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\tgoods_num(newVal, oldVal) {\r\n\t\t\t\tif(this.price){\r\n\t\t\t\t\tthis.total = (Number(newVal) * Number(this.price)).toFixed(2);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\tif (res.from === 'button') {\r\n\t\t\t\tconsole.log(res.target)\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/pages/index/index'\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetPstel(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.getPstel(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.ps_tel = data\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetTel(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {};\r\n\t\t\t\tthat.$api.tel(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.kefu_tel = data\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 柜子信息\r\n\t\t\tgetInfo(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\t'machine-no': that.machine_no,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.machineindex(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.machine_info = data.machine // 保存机器完整信息\r\n\t\t\t\t\t\tthat.banner = data.ad\r\n\t\t\t\t\t\tthat.way = data.way\r\n\t\t\t\t\t\tthat.notice = data.notice\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择购买方式\r\n\t\t\tsetDeliveryType(type){\r\n\t\t\t\tthis.delivery_type = type\r\n\t\t\t},\r\n\r\n\t\t\t// 选择规格\r\n\t\t\tsetGoods(e){\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.goods_id = e.id\r\n\t\t\t\tif(e.spec_num > 0){\r\n\t\t\t\t\tthat.goods_num = 1\r\n\t\t\t\t\tthat.price = ''\r\n\t\t\t\t\tthat.total = e.price\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.goods_num = ''\r\n\t\t\t\t\tthat.price = e.price\r\n\t\t\t\t\tthat.$refs.showModal.open()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetClose(e){\r\n\t\t\t\tthis.$refs.showModal.close()\r\n\t\t\t\tif(e == 1){\r\n\t\t\t\t\tthis.goods_id = ''\r\n\t\t\t\t\tthis.goods_num = ''\r\n\t\t\t\t\tthis.price = ''\r\n\t\t\t\t\tthis.total = ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 下单\r\n\t\t\tgetSubmit(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif(that.isBtn){\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!that.$cache.fetchCache('token')) {\r\n\t\t\t\t\tthat.$util.toLogin()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif(!that.goods_id){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\ttitle: '请选择规格',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 如果是外卖模式，跳转到订单确认页面\r\n\t\t\t\tif (that.delivery_type == 2) {\r\n\t\t\t\t\t// 获取当前选中的商品信息\r\n\t\t\t\t\tlet selectedGoods = that.way.find(item => item.id == that.goods_id)\r\n\r\n\t\t\t\t\t// 计算实际重量：如果是自定义重量，使用goods_num作为重量；否则使用商品默认重量\r\n\t\t\t\t\tlet actualWeight = selectedGoods ? (selectedGoods.weight || 10) : 10\r\n\t\t\t\t\tlet actualQuantity = 1 // 外卖模式默认数量为1\r\n\r\n\t\t\t\t\t// 如果选择的是自定义规格，goods_num表示重量而不是数量\r\n\t\t\t\t\tif (selectedGoods && selectedGoods.name && selectedGoods.name.includes('自定义')) {\r\n\t\t\t\t\t\tactualWeight = parseFloat(that.goods_num) || 10\r\n\t\t\t\t\t\tactualQuantity = 1\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet goodsInfo = {\r\n\t\t\t\t\t\tmachine_no: that.machine_no,\r\n\t\t\t\t\t\tmachine_id: that.machine_info ? that.machine_info.id : null, // 添加机器ID\r\n\t\t\t\t\t\tgoods_id: that.goods_id,\r\n\t\t\t\t\t\tgoods_num: actualQuantity, // 修正：使用实际数量\r\n\t\t\t\t\t\tgoods_name: selectedGoods ? selectedGoods.name : '冰块',\r\n\t\t\t\t\t\tgoods_price: that.price || that.total || (selectedGoods ? selectedGoods.price : 79.00),\r\n\t\t\t\t\t\tgoods_spec: selectedGoods ? selectedGoods.spec : '冰块10kg',\r\n\t\t\t\t\t\tgoods_weight: actualWeight // 修正：使用实际重量\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/pagesHome/order-confirm?goodsInfo=' + encodeURIComponent(JSON.stringify(goodsInfo))\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 自提模式：保持原有逻辑\r\n\t\t\t\tthat.isBtn = true\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\t'machine-no': that.machine_no,\r\n\t\t\t\t\tgoods_id: that.goods_id,\r\n\t\t\t\t\tgoods_num: that.goods_num,\r\n\t\t\t\t};\r\n\t\t\t\tthat.$api.createOrderM(params).then(res => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\tmsg,\r\n\t\t\t\t\t\ttime\r\n\t\t\t\t\t} = res.data\r\n\t\t\t\t\tif (code == 1) {\r\n\t\t\t\t\t\tthat.isBtn = false\r\n\t\t\t\t\t\t// 自提订单直接跳转支付页面\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/pagesPay/index?type=1&order_sn=' + data.order_sn\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.isBtn = false\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch((err) => {\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 客服热线\r\n\t\t\tgetCall(e) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: e == 1?that.ps_tel:that.kefu_tel\r\n\t\t\t\t}).catch((res) => {\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.content{\r\n\t\tpadding-bottom: 30rpx;\r\n\t}\r\n\t.banner_bg .banner {\r\n\t\twidth: 100%;\r\n\t\theight: 488rpx;\r\n\t}\r\n\r\n\t.banner_bg .banner .swiper_image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.nr {\r\n\t\tbackground: linear-gradient( 180deg, #BAD7FF 0%, #FFFFFF 32%);\r\n\t}\r\n\t.nr .title{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 28rpx 30rpx;\r\n\t}\r\n\t.nr .title .xx view{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\t.nr .title .xx view:first-child{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #000;\r\n\t\tfont-weight: bold;\r\n\t\tbackground: url(data:image/png;base64,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) no-repeat left bottom;\r\n\t\tbackground-size: auto 20rpx; \r\n\t}\r\n\t.nr .title .text{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.specification{\r\n\t\tbackground: #fff;\r\n\t\tpadding: 25rpx 30rpx 40rpx;\r\n\t\tborder-radius: 16rpx 16rpx 0 0;\r\n\t}\r\n\t.specification .bt {\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t\tpadding-left: 54rpx;\r\n\t\tmargin-bottom: 32rpx;\r\n\t\tbackground: url(data:image/png;base64,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) no-repeat left center;\r\n\t\tbackground-size: 40rpx auto;\r\n\t}\r\n\t\r\n\t.specification .list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t.specification .list .xx{\r\n\t\tborder: 1rpx solid #BFBFBF;\r\n\t\tmargin-top: 30rpx;\r\n\t\tmargin-right: 30rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\toverflow: hidden;\r\n\t\twidth: 210rpx;\r\n\t\theight: 142rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.specification .list .xx.on{\r\n\t\tborder-color: #3478FB;\r\n\t\tbackground: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAYAAAA6RwvCAAAAAXNSR0IArs4c6QAAA+BJREFUWEfFmGuIVGUYx///c86cdXMbSXcDqciki2Ds7Qz0pYLUoiBDv5jFopa0QVHZRc/sFkQQ28zKgisVFnQxKoMiFxRSTM0PfUg7s65kkpEgQkHtRruVMXMuT5wZZ/Y2Z87ZmbHeb4f3ef7P733e63OIObTlT0hTw5XuXaSsJHALgGsBXHdJ4gLACwI5K8LD2T/Vo6ff5F9R5RnF0OjJ3UaPLwuwikQsio8IbAJfiqK9Yr3Gb8J8KoIkeuVWcZ1XATxAIhL0zIAiEBBD4mkvZvp5JggoULzTzHaRyjsE9LDRROkXICfibc6kGz4sZ18WxDDtJIC+arMQBFbIjrLVSqkDM21mgRg97tMUbzDKKKu1EZEtVlqfFmMaSGfSXqEABwFo1QaJ6Oe4Hu4d7o8dLtqXQNqevXhNTI+dArEwoliNZhxzvWzrcP/8n32hEohh5t4jualG9Tm6y7vfpvTNJZA2M7tco3KKgDJHpVrNHVu09pE0T+czYpjOXlLW1Kpajb+An1opbR2NbrmCC50xAPOqEQryaZoHdN2hwDonOPGTVJK+KL9rLTS2ZddQUfbWE2JRE7CrW8WSFsJxgfWDDs7/FhxBPG8tE9uct6BId71ApkL4mq4HPLgjBATcRSNpHyRwTz1AykH07nFx5LuKU+OHPuCDnCGwLAzkpsUFix9/KW9ZAwQE+J6JpD0OIF4J5PZlxPYuNW8y+IWLT76ePsJaIHxNEfwRCeSxlQq6V00eMQP7J2FqhbiUgAkf5AcAN1fKSKMO7HxERfuSyavJhzk0IqXdUVyYEdfEzHBnmTDtYyDuDFsj5WBGJwTN8QKcvzuqhPCn5isaSWc3IRvCQPz+cjC1QuTXCPg+O8zsepXKniggRZjXH1XRen0hE54APR9H2qKBIVzxHuKNT43FF8yP/0qgISpMvBF4+3EVS68mUkMuPj8eek4ESvtPyPG/J1r+/0tPOGSltbV5kPatuXZVpfVfPwME8BzxWkfSDYVngN8MM/cRyYejTk897ETkAyutb/S1SiCtL/xzg65pwwAW1CNIuAbHXdtuGx5oPD8NxP/oMJ37VMg+EIXz/PI1xwNWZ1KxA8UQs8sJ036OxKy6o55M4skzVr++c6pm2QKr08w9qZA7LkNZ4XgiWzJp/Y2ZAwsuOZP2CgKfEbiqTtkY84B1mVTsSDm9ioW10SuL4dp9IDdUvbXFv4a4m6r6ktXHgNfMlF1TadSJZK4DYB8Ed0deyAIXxCHHlZ6T2/WTYVmd068G43lpFtVerZD3A1gKchFEmvNByFGI+NXAOU9kP93YPmuAo2EAxf5/AU6Eibh+lv02AAAAAElFTkSuQmCC) no-repeat top -1rpx right -1rpx;\r\n\t\tbackground-size: 34rpx auto;\r\n\t}\r\n\t.specification .list .xx:nth-child(3n){\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t.specification .list .xx:nth-child(-n + 3){\r\n\t\tmargin-top: 0;\r\n\t}\r\n\t.specification .list .xx view{\r\n\t\tfont-size: 36rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t.specification .list .xx text{\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tcolor: #161513;\r\n\t}\r\n\t.specification .list .xx.on text{\r\n\t\tcolor: #FC451E;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.specification .btn {\r\n\t\tmargin-top: 68rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground: #3478FB;\r\n\t\tborder-radius: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 购买方式选择样式 */\r\n\t.delivery-type {\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\r\n\t.delivery-buttons {\r\n\t\tdisplay: flex;\r\n\t\tborder-radius: 8rpx;\r\n\t\toverflow: hidden;\r\n\t\theight: 88rpx;\r\n\t}\r\n\r\n\t.delivery-btn {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbackground: #F5F5F5;\r\n\t\tborder: 1rpx solid #E5E5E5;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.delivery-btn:first-child {\r\n\t\tborder-right: none;\r\n\t}\r\n\r\n\t.delivery-btn.active {\r\n\t\tbackground: #4A90E2;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.delivery-btn text {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.delivery-btn.active text {\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.delivery-btn .distance {\r\n\t\tfont-size: 22rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.delivery-btn.active .distance {\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t}\r\n\t.message{\r\n\t\tbackground: #fff;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 24rpx;\r\n\t}\r\n\t.message .xx{\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\twidth: 50%;\r\n\t}\r\n\t.message .xx image{\r\n\t\twidth: 44rpx;\r\n\t\theight: auto;\r\n\t}\r\n\t.message .xx .text{\r\n\t\tmargin-left: 28rpx;\r\n\t}\r\n\t.message .xx .text view{\r\n\t\tfont-size: 22rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.message .xx .text view:first-child{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 38rpx;\r\n\t\tcolor: #161513;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.tc_input{\r\n\t\twidth: 630rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 38rpx 30rpx 70rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.tc_input .title{\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #161513;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.tc_input input{\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tcolor: #161513;\r\n\t\twidth: 100%;\r\n\t\theight: 90rpx;\r\n\t\tborder-bottom: 1rpx solid #E4E4E4;\r\n\t}\r\n\t.tc_input .btn{\r\n\t\tmargin-top: 72rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.tc_input .btn view{\r\n\t\twidth: 46%;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 72rpx;\r\n\t\tcolor: #5A9AF1;\r\n\t\tborder: 1rpx solid #5A9AF1;\r\n\t\tborder-radius: 46rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.tc_input .btn view:last-child{\r\n\t\tbackground: #5A9AF1;\r\n\t\tcolor: #fff;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vending.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vending.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1757295300000\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}